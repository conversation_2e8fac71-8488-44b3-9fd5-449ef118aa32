#!/bin/bash
# Set Java environment
export JAVA_HOME="/mnt/c/Program Files/Java/jdk-21"
export PATH="/mnt/c/Program Files/Java/jdk-21/bin:$PATH"

# Load .env file if it exists
if [ -f .env ]; then
    export $(cat .env | grep -v ^# | xargs)
fi

# Run the application
echo "Starting YouTube Transcript API..."
cd transcript-api
/mnt/c/Users/<USER>/Documents/dev/july/youtube-transcribe/mvnw.cmd clean compile spring-boot:run