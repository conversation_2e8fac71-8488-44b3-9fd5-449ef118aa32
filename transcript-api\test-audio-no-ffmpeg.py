#!/usr/bin/env python3
"""
Test audio extraction without ffmpeg conversion
"""

import subprocess
import os
import tempfile

def test_audio_extraction():
    """Test audio extraction without conversion"""
    print("Testing audio extraction without ffmpeg...")
    
    video_id = "dQw4w9WgXcQ"
    
    with tempfile.TemporaryDirectory() as temp_dir:
        output_template = os.path.join(temp_dir, f"test_audio_{video_id}.%(ext)s")
        
        cmd = [
            "python", "-m", "yt_dlp",
            "-x",  # Extract audio
            "--audio-format", "best",  # Keep original format
            "--audio-quality", "5",
            "-o", output_template,
            "--no-playlist",
            "--max-filesize", "5M",
            f"https://www.youtube.com/watch?v={video_id}"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print(f"Exit code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"STDERR:\n{result.stderr}")
        
        # Check what files were created
        print("\nFiles created:")
        for file in os.listdir(temp_dir):
            file_path = os.path.join(temp_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")

if __name__ == "__main__":
    test_audio_extraction()