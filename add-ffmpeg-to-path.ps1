# PowerShell script to add ffmpeg to PATH

$ffmpegPath = "C:\ffmpeg\bin"

# Get current PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", "User")

# Check if ffmpeg is already in PATH
if ($currentPath -split ';' | Where-Object { $_ -eq $ffmpegPath }) {
    Write-Host "ffmpeg is already in PATH" -ForegroundColor Green
} else {
    # Add ffmpeg to PATH
    $newPath = $currentPath + ";" + $ffmpegPath
    [Environment]::SetEnvironmentVariable("Path", $newPath, "User")
    
    # Also update current session
    $env:Path = $env:Path + ";" + $ffmpegPath
    
    Write-Host "Added ffmpeg to PATH successfully!" -ForegroundColor Green
    Write-Host "  Path added: $ffmpegPath" -ForegroundColor Cyan
}

# Test ffmpeg
Write-Host "`nTesting ffmpeg..." -ForegroundColor Yellow
& ffmpeg -version | Select-Object -First 1

Write-Host "`nffmpeg is ready to use!" -ForegroundColor Green
Write-Host "Note: You may need to restart your terminal for the changes to take effect in other sessions." -ForegroundColor Yellow