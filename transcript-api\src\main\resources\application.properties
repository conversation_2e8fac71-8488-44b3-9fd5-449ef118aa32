server.port=8080
spring.application.name=youtube-transcript-api
spring.mvc.async.request-timeout=30000

# Logging configuration
logging.level.com.youtube.transcript=DEBUG
logging.level.root=INFO
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# OpenAI Configuration
# Reads from environment variable OPENAI_API_KEY (set in .env file)
openai.api.key=${OPENAI_API_KEY:********************************************************************************************************************************************************************}
openai.api.timeout=300