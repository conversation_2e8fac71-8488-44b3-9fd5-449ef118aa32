#!/usr/bin/env python3
"""
Simple test script to verify yt-dlp can extract audio from YouTube
"""

import subprocess
import sys
import os
import tempfile

def run_command(cmd):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(f"Exit code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Command timed out")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def test_yt_dlp():
    """Test yt-dlp installation and basic functionality"""
    print("=== Testing yt-dlp installation ===")
    
    # Test 1: Check if yt-dlp is available
    print("\n1. Testing yt-dlp version...")
    if not run_command(["python", "-m", "yt_dlp", "--version"]):
        print("❌ yt-dlp is not available via 'python -m yt_dlp'")
        return False
    
    # Test 2: Test with a simple video
    print("\n2. Testing audio extraction...")
    video_id = "dQw4w9WgXcQ"  # Rick Roll video
    
    with tempfile.TemporaryDirectory() as temp_dir:
        output_file = os.path.join(temp_dir, f"test_audio_{video_id}.mp3")
        
        cmd = [
            "python", "-m", "yt_dlp",
            "-x",  # Extract audio
            "--audio-format", "mp3",
            "--audio-quality", "5",
            "-o", output_file.replace(".mp3", ".%(ext)s"),
            "--no-playlist",
            "--max-filesize", "5M",  # Limit to 5MB for testing
            f"https://www.youtube.com/watch?v={video_id}"
        ]
        
        if run_command(cmd):
            # Check if file was created
            for file in os.listdir(temp_dir):
                if file.startswith(f"test_audio_{video_id}"):
                    file_path = os.path.join(temp_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"✅ Audio extracted successfully: {file} ({file_size} bytes)")
                    return True
            print("❌ Audio file was not created")
            return False
        else:
            print("❌ Audio extraction failed")
            return False

def test_openai_api():
    """Test OpenAI API key"""
    print("\n=== Testing OpenAI API key ===")
    
    # Check if API key is set
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY environment variable not set")
        return False
    
    if len(api_key) < 20:
        print("❌ API key seems too short")
        return False
    
    print(f"✅ OpenAI API key found (length: {len(api_key)})")
    return True

if __name__ == "__main__":
    print("YouTube Transcript API - Debug Test")
    print("=" * 50)
    
    # Load environment variables from .env file
    env_file = os.path.join(os.path.dirname(__file__), "..", ".env")
    if os.path.exists(env_file):
        print(f"Loading .env file from: {env_file}")
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    success = True
    
    # Test yt-dlp
    if not test_yt_dlp():
        success = False
    
    # Test OpenAI API
    if not test_openai_api():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! The system should work correctly.")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    sys.exit(0 if success else 1)