@echo off
echo Cleaning up and starting fresh...

REM Kill any Java processes that might be stuck
taskkill /F /IM java.exe 2>nul
timeout /t 2 >nul

REM Set environment
set JAVA_HOME=C:\Program Files\Java\jdk-21
set PATH=%JAVA_HOME%\bin;%PATH%

REM Load API key
if exist ..\\.env (
    for /f "usebackq tokens=1,2 delims==" %%a in ("..\\.env") do (
        if "%%a"=="OPENAI_API_KEY" (
            set OPENAI_API_KEY=%%b
        )
    )
)

echo Starting with clean environment...
echo OPENAI_API_KEY: %OPENAI_API_KEY:~0,20%...

REM Start the application
mvnw.cmd spring-boot:run -Dspring-boot.run.arguments="--server.port=8080 --logging.level.com.youtube.transcript=DEBUG"