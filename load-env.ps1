# Simple .env file loader for PowerShell
# Usage: . .\load-env.ps1

if (Test-Path ".env") {
    Write-Host "Loading environment variables from .env file..." -ForegroundColor Green
    
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            Set-Item -Path "env:$name" -Value $value
            Write-Host "  $name = $value" -ForegroundColor Cyan
        }
    }
    
    Write-Host "✓ Environment variables loaded successfully" -ForegroundColor Green
}
else {
    Write-Host "⚠ .env file not found" -ForegroundColor Yellow
    Write-Host "Create a .env file with your environment variables:" -ForegroundColor Yellow
    Write-Host "  OPENAI_API_KEY=your-api-key-here" -ForegroundColor Cyan
}