#!/usr/bin/env python3
"""
Direct test of transcript extraction components
"""

import subprocess
import os
import sys
import tempfile
import json

def test_env():
    """Test environment setup"""
    print("=== Testing Environment ===")
    
    # Load .env file
    env_file = os.path.join(os.path.dirname(__file__), "..", ".env")
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"[OK] OpenAI API key found: {api_key[:10]}...")
        return True
    else:
        print("[ERROR] OpenAI API key not found")
        return False

def test_yt_dlp_extraction():
    """Test yt-dlp audio extraction"""
    print("\n=== Testing yt-dlp Audio Extraction ===")
    
    video_id = "dQw4w9WgXcQ"
    with tempfile.TemporaryDirectory() as temp_dir:
        output_file = os.path.join(temp_dir, f"audio_{video_id}.mp3")
        
        cmd = [
            "python", "-m", "yt_dlp",
            "-x",
            "--audio-format", "mp3",
            "--ffmpeg-location", "C:\\ffmpeg\\bin",
            "-o", output_file,
            "--no-playlist",
            "--max-filesize", "10M",
            f"https://www.youtube.com/watch?v={video_id}"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Check if file was created
            files = [f for f in os.listdir(temp_dir) if f.startswith(f"audio_{video_id}")]
            if files:
                file_path = os.path.join(temp_dir, files[0])
                file_size = os.path.getsize(file_path)
                print(f"[OK] Audio extracted: {files[0]} ({file_size} bytes)")
                return file_path
            else:
                print("[ERROR] No audio file created")
                return None
        else:
            print(f"[ERROR] yt-dlp failed: {result.stderr}")
            return None

def test_whisper_api(audio_file):
    """Test OpenAI Whisper API"""
    print("\n=== Testing OpenAI Whisper API ===")
    
    import requests
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("[ERROR] No API key available")
        return False
    
    url = "https://api.openai.com/v1/audio/transcriptions"
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    with open(audio_file, 'rb') as f:
        files = {
            'file': (os.path.basename(audio_file), f, 'audio/mpeg'),
            'model': (None, 'whisper-1'),
            'language': (None, 'en')
        }
        
        print("Sending request to OpenAI...")
        response = requests.post(url, headers=headers, files=files)
    
    if response.status_code == 200:
        result = response.json()
        print(f"[OK] Transcription successful!")
        print(f"Text preview: {result.get('text', '')[:100]}...")
        return True
    else:
        print(f"[ERROR] API request failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def test_caption_extraction():
    """Test caption extraction directly"""
    print("\n=== Testing Caption Extraction ===")
    
    import requests
    
    video_id = "dQw4w9WgXcQ"
    
    # Try different caption URLs
    urls = [
        f"https://www.youtube.com/api/timedtext?v={video_id}&lang=en&fmt=srv3",
        f"https://www.youtube.com/api/timedtext?v={video_id}&lang=en-US&fmt=srv3",
        f"https://www.youtube.com/api/timedtext?v={video_id}&lang=a.en&fmt=srv3"
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "*/*",
        "Accept-Language": "en-US,en;q=0.9"
    }
    
    for url in urls:
        print(f"Trying: {url}")
        response = requests.get(url, headers=headers)
        print(f"  Status: {response.status_code}, Length: {len(response.text)}")
        if response.text.strip():
            print(f"  [OK] Got captions!")
            return True
    
    print("[ERROR] No captions found")
    return False

if __name__ == "__main__":
    print("YouTube Transcript API - Direct Testing")
    print("=" * 50)
    
    # Test 1: Environment
    env_ok = test_env()
    
    # Test 2: yt-dlp extraction
    audio_file = test_yt_dlp_extraction()
    
    # Test 3: Whisper API (if audio extracted)
    if audio_file and env_ok:
        test_whisper_api(audio_file)
    
    # Test 4: Caption extraction
    test_caption_extraction()
    
    print("\n" + "=" * 50)
    print("Testing complete!")