package com.youtube.transcript.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/debug")
public class DebugController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient httpClient = HttpClient.newHttpClient();
    
    @GetMapping("/youtube-data")
    public ResponseEntity<Map<String, Object>> getYouTubeData(@RequestParam String videoId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String url = "https://www.youtube.com/watch?v=" + videoId;
            logger.info("Fetching YouTube page: {}", url);
            
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .build();
            
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            String html = response.body();
            
            result.put("status", "success");
            result.put("htmlLength", html.length());
            
            // Extract title
            Document doc = Jsoup.parse(html);
            String title = doc.select("meta[property=og:title]").attr("content");
            result.put("title", title);
            
            // Try to find player response
            String playerResponse = extractPlayerResponse(html);
            if (playerResponse != null) {
                result.put("playerResponseFound", true);
                result.put("playerResponseLength", playerResponse.length());
                
                // Parse player response
                try {
                    JsonNode playerData = objectMapper.readTree(playerResponse);
                    
                    // Check for captions
                    JsonNode captions = playerData.path("captions");
                    if (!captions.isMissingNode()) {
                        result.put("captionsFound", true);
                        
                        JsonNode captionTracks = captions.path("playerCaptionsTracklistRenderer")
                                .path("captionTracks");
                        
                        if (!captionTracks.isMissingNode() && captionTracks.isArray()) {
                            result.put("captionTracksCount", captionTracks.size());
                            
                            // Get first caption track details
                            if (captionTracks.size() > 0) {
                                JsonNode firstTrack = captionTracks.get(0);
                                Map<String, Object> trackInfo = new HashMap<>();
                                trackInfo.put("baseUrl", firstTrack.path("baseUrl").asText(""));
                                trackInfo.put("languageCode", firstTrack.path("languageCode").asText(""));
                                trackInfo.put("name", firstTrack.path("name").path("simpleText").asText(""));
                                result.put("firstTrack", trackInfo);
                                
                                // Try to fetch transcript from first track
                                String captionUrl = firstTrack.path("baseUrl").asText("");
                                if (!captionUrl.isEmpty()) {
                                    result.put("captionUrl", captionUrl);
                                    
                                    try {
                                        HttpRequest captionRequest = HttpRequest.newBuilder()
                                                .uri(URI.create(captionUrl))
                                                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                                                .header("Accept", "*/*")
                                                .header("Accept-Language", "en-US,en;q=0.9")
                                                .header("Referer", "https://www.youtube.com/")
                                                .build();
                                        
                                        HttpResponse<String> captionResponse = httpClient.send(captionRequest, 
                                                HttpResponse.BodyHandlers.ofString());
                                        
                                        result.put("captionFetchStatus", captionResponse.statusCode());
                                        result.put("captionResponseLength", captionResponse.body().length());
                                        result.put("captionResponseHeaders", captionResponse.headers().map());
                                        
                                        String responseBody = captionResponse.body();
                                        if (responseBody.length() > 0) {
                                            result.put("captionResponsePreview", responseBody.substring(0, 
                                                    Math.min(1000, responseBody.length())));
                                        } else {
                                            result.put("captionResponsePreview", "EMPTY_RESPONSE");
                                        }
                                        
                                        // Try alternative URL format
                                        if (responseBody.isEmpty()) {
                                            String altUrl = captionUrl + "&fmt=srv3";
                                            result.put("alternativeCaptionUrl", altUrl);
                                            
                                            HttpRequest altRequest = HttpRequest.newBuilder()
                                                    .uri(URI.create(altUrl))
                                                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                                                    .header("Accept", "*/*")
                                                    .header("Accept-Language", "en-US,en;q=0.9")
                                                    .header("Referer", "https://www.youtube.com/")
                                                    .build();
                                            
                                            HttpResponse<String> altResponse = httpClient.send(altRequest, 
                                                    HttpResponse.BodyHandlers.ofString());
                                            
                                            result.put("altCaptionFetchStatus", altResponse.statusCode());
                                            result.put("altCaptionResponseLength", altResponse.body().length());
                                            
                                            if (altResponse.body().length() > 0) {
                                                result.put("altCaptionResponsePreview", altResponse.body().substring(0, 
                                                        Math.min(1000, altResponse.body().length())));
                                            }
                                        }
                                        
                                    } catch (Exception e) {
                                        result.put("captionFetchError", e.getMessage());
                                    }
                                } else {
                                    result.put("captionUrl", "EMPTY_URL");
                                }
                            }
                        } else {
                            result.put("captionTracksCount", 0);
                        }
                    } else {
                        result.put("captionsFound", false);
                    }
                    
                } catch (Exception e) {
                    result.put("playerResponseParseError", e.getMessage());
                }
            } else {
                result.put("playerResponseFound", false);
            }
            
            // Check for captionTracks in raw HTML
            Pattern pattern = Pattern.compile("\"captionTracks\":\\[(.*?)\\]", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                result.put("rawCaptionTracksFound", true);
                String captionData = matcher.group(1);
                result.put("rawCaptionDataLength", captionData.length());
                result.put("rawCaptionDataPreview", captionData.substring(0, Math.min(200, captionData.length())));
            } else {
                result.put("rawCaptionTracksFound", false);
            }
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            logger.error("Debug error", e);
        }
        
        return ResponseEntity.ok(result);
    }
    
    private String extractPlayerResponse(String html) {
        String[] patterns = {
            "var ytInitialPlayerResponse = (\\{.*?\\});",
            "ytInitialPlayerResponse\":(\\{.*?\\}),\"",
            "\"player\":(\\{.*?\\}),\"",
            "ytInitialPlayerResponse = (\\{.*?\\});"
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        
        return null;
    }
}