@echo off
echo ==============================================
echo Debug Transcript Extraction
echo ==============================================

REM Set environment
set JAVA_HOME=C:\Program Files\Java\jdk-21
set PATH=%JAVA_HOME%\bin;%PATH%

REM Load API key
if exist ..\\.env (
    for /f "usebackq tokens=1,2 delims==" %%a in ("..\\.env") do (
        if "%%a"=="OPENAI_API_KEY" (
            set OPENAI_API_KEY=%%b
        )
    )
)

echo.
echo Environment:
echo - JAVA_HOME: %JAVA_HOME%
echo - OPENAI_API_KEY: %OPENAI_API_KEY:~0,20%...
echo.

REM Compile and run with debug logging
echo Starting application with DEBUG logging...
mvnw.cmd clean compile spring-boot:run -Dspring-boot.run.arguments="--logging.level.com.youtube.transcript=DEBUG"