#!/usr/bin/env pwsh

Write-Host "YouTube Transcript API Setup" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

Write-Host "`n1. Checking Java installation..." -ForegroundColor Yellow
if (Test-Command "java") {
    java -version
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Java not working properly." -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "ERROR: Java not found. Please install Java 17 or later." -ForegroundColor Red
    Write-Host "Download from: https://adoptium.net/" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n2. Checking Maven..." -ForegroundColor Yellow
Set-Location "transcript-api"
if (Test-Path "mvnw.cmd") {
    .\mvnw.cmd --version
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Maven wrapper not working." -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "ERROR: Maven wrapper not found." -ForegroundColor Red
    exit 1
}

Write-Host "`n3. Checking yt-dlp installation..." -ForegroundColor Yellow
if (Test-Command "yt-dlp") {
    yt-dlp --version
    Write-Host "✓ yt-dlp found" -ForegroundColor Green
}
else {
    Write-Host "WARNING: yt-dlp not found. Installing via pip..." -ForegroundColor Yellow
    if (Test-Command "pip") {
        pip install yt-dlp
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ yt-dlp installed successfully" -ForegroundColor Green
        }
        else {
            Write-Host "ERROR: Failed to install yt-dlp via pip." -ForegroundColor Red
            Write-Host "Please install manually from: https://github.com/yt-dlp/yt-dlp/releases" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "ERROR: pip not found. Cannot install yt-dlp automatically." -ForegroundColor Red
        Write-Host "Please install yt-dlp manually from: https://github.com/yt-dlp/yt-dlp/releases" -ForegroundColor Yellow
    }
}

Write-Host "`n4. Checking FFmpeg..." -ForegroundColor Yellow
if (Test-Command "ffmpeg") {
    Write-Host "✓ FFmpeg found" -ForegroundColor Green
}
else {
    Write-Host "WARNING: FFmpeg not found." -ForegroundColor Yellow
    Write-Host "Please install FFmpeg from: https://ffmpeg.org/download.html" -ForegroundColor Yellow
    Write-Host "Or use: winget install FFmpeg" -ForegroundColor Cyan
}

Write-Host "`n5. Checking OpenAI API Key..." -ForegroundColor Yellow

# Load .env file if it exists
if (Test-Path ".env") {
    Write-Host "Loading .env file..." -ForegroundColor Cyan
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            Set-Item -Path "env:$name" -Value $value
        }
    }
}

if ($env:OPENAI_API_KEY) {
    Write-Host "✓ OpenAI API key found" -ForegroundColor Green
}
else {
    Write-Host "WARNING: OPENAI_API_KEY not set." -ForegroundColor Yellow
    $apiKey = Read-Host "Enter your OpenAI API key (or press Enter to skip)"
    if ($apiKey) {
        $env:OPENAI_API_KEY = $apiKey
        Write-Host "✓ API key set for this session" -ForegroundColor Green
        
        # Ask if user wants to save to .env file
        $save = Read-Host "Save API key to .env file for future use? (y/n)"
        if ($save -eq "y" -or $save -eq "Y") {
            "OPENAI_API_KEY=$apiKey" | Out-File -FilePath ".env" -Encoding UTF8
            Write-Host "✓ API key saved to .env file" -ForegroundColor Green
        }
    }
    else {
        Write-Host "Continuing without AI transcription..." -ForegroundColor Yellow
    }
}

Write-Host "`n6. Building the application..." -ForegroundColor Yellow
.\mvnw.cmd clean compile -DskipTests
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Build failed." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "✓ Build successful" -ForegroundColor Green

Write-Host "`n7. Setup complete! Starting the application..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server when done." -ForegroundColor Cyan
Write-Host ""
Write-Host "Test endpoints:" -ForegroundColor Yellow
Write-Host "  Health: http://localhost:8080/api/health/status" -ForegroundColor Cyan
Write-Host "  Extract: http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ" -ForegroundColor Cyan
Write-Host ""

# Start the application (skip tests completely)
.\mvnw.cmd spring-boot:run -DskipTests