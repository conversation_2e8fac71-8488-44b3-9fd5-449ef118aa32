import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

public class TestYtDlp {
    public static void main(String[] args) {
        try {
            System.out.println("Testing yt-dlp availability...");
            
            ProcessBuilder pb = new ProcessBuilder("yt-dlp.bat", "--version");
            pb.directory(new java.io.File("."));
            
            Process process = pb.start();
            
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            
            if (finished && process.exitValue() == 0) {
                System.out.println("✓ yt-dlp is available!");
                System.out.println("Version: " + output.toString().trim());
            } else {
                System.out.println("✗ yt-dlp failed with exit code: " + process.exitValue());
                System.out.println("Output: " + output.toString());
            }
            
        } catch (Exception e) {
            System.out.println("✗ Error testing yt-dlp: " + e.getMessage());
            e.printStackTrace();
        }
    }
}