@echo off
echo YouTube Transcript API Setup
echo ============================

echo.
echo 1. Checking Java installation...
java -version
if %errorlevel% neq 0 (
    echo ERROR: Java not found. Please install Java 17 or later.
    pause
    exit /b 1
)

echo.
echo 2. Checking Maven...
cd transcript-api
call mvnw.cmd --version
if %errorlevel% neq 0 (
    echo ERROR: Maven wrapper not working.
    pause
    exit /b 1
)

echo.
echo 3. Checking yt-dlp installation...
yt-dlp --version
if %errorlevel% neq 0 (
    echo WARNING: yt-dlp not found. Installing via pip...
    pip install yt-dlp
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install yt-dlp. Please install manually.
        echo Visit: https://github.com/yt-dlp/yt-dlp/releases
        pause
    )
)

echo.
echo 4. Checking FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: FFmpeg not found. Please install FFmpeg manually.
    echo Visit: https://ffmpeg.org/download.html
)

echo.
echo 5. Checking OpenAI API Key...
if "%OPENAI_API_KEY%"=="" (
    echo WARNING: OPENAI_API_KEY not set.
    set /p api_key="Enter your OpenAI API key (or press Enter to skip): "
    if not "!api_key!"=="" (
        set OPENAI_API_KEY=!api_key!
        echo API key set for this session.
    )
)

echo.
echo 6. Building the application...
call mvnw.cmd clean compile -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Build failed.
    pause
    exit /b 1
)

echo.
echo 7. Setup complete! Starting the application...
echo Press Ctrl+C to stop the server when done.
echo.
echo Test endpoints:
echo   Health: http://localhost:8080/api/health/status
echo   Extract: http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ
echo.

call mvnw.cmd spring-boot:run