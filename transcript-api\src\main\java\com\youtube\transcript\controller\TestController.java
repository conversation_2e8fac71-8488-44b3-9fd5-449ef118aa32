package com.youtube.transcript.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {
    
    private final HttpClient httpClient = HttpClient.newHttpClient();
    
    @GetMapping("/direct-caption")
    public ResponseEntity<Map<String, Object>> testDirectCaption(@RequestParam String videoId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Test with a known working video that has captions
            String testVideoId = "dQw4w9WgXcQ"; // Rick <PERSON> - Never Gonna Give You Up (known to have captions)
            if (videoId.equals("test")) {
                videoId = testVideoId;
            }
            
            // Try multiple approaches
            String[] attempts = {
                String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en&fmt=srv3", videoId),
                String.format("https://www.youtube.com/api/timedtext?v=%s&lang=a.en&fmt=srv3", videoId),
                String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en&fmt=srv1", videoId),
                String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en-US&fmt=srv3", videoId),
                String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en&fmt=json3", videoId)
            };
            
            for (int i = 0; i < attempts.length; i++) {
                String url = attempts[i];
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                        .header("Accept", "*/*")
                        .header("Accept-Language", "en-US,en;q=0.9")
                        .header("Referer", "https://www.youtube.com/")
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                
                Map<String, Object> attemptResult = new HashMap<>();
                attemptResult.put("url", url);
                attemptResult.put("status", response.statusCode());
                attemptResult.put("responseLength", response.body().length());
                attemptResult.put("headers", response.headers().map());
                
                if (response.body().length() > 0) {
                    attemptResult.put("responsePreview", response.body().substring(0, Math.min(500, response.body().length())));
                    attemptResult.put("fullResponse", response.body());
                } else {
                    attemptResult.put("responsePreview", "EMPTY");
                }
                
                result.put("attempt" + (i + 1), attemptResult);
                
                // If we found content, break
                if (response.body().length() > 0) {
                    result.put("successfulAttempt", i + 1);
                    break;
                }
            }
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(result);
    }
}