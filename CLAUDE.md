# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack YouTube transcript extraction application with:
- **Frontend**: React SPA that provides a user interface for extracting YouTube transcripts
- **Backend**: Spring Boot REST API that handles the actual transcript extraction from YouTube

## Commands

### Frontend (youtube-transcript-frontend/)
```bash
npm start          # Start development server on http://localhost:3000
npm test           # Run tests in watch mode
npm run build      # Create production build in /build folder
npm test -- --coverage  # Run tests with coverage report
```

### Backend (transcript-api/)
```bash
./mvnw spring-boot:run   # Run the Spring Boot application on port 8080
./mvnw clean install     # Build the project
./mvnw package          # Create JAR file
./mvnw test             # Run tests
```

## Architecture

### Frontend Architecture
- **Main Component**: `src/App.js` - Entry point with layout
- **Core Component**: `src/components/TranscriptExtractor.js` - Handles transcript extraction UI
- **Styling**: Tailwind CSS with utility classes
- **State Management**: React hooks (useState)
- **API Communication**: Fetch API to backend on port 8080

### Backend Architecture
- **Controller Layer**: `TranscriptController` handles HTTP requests at `/api/transcript/extract`
- **Service Layer**: Multi-layered transcript extraction approach:
  - **Primary method**: AI transcription using OpenAI Whisper API (most accurate)
  - **Fallback method 1**: Direct YouTube timedtext API
  - **Fallback method 2**: YouTube page HTML caption extraction
  - **Fallback method 3**: YouTube InnerTube API
- **Audio Extraction**: `AudioExtractionService` uses yt-dlp/youtube-dl to extract audio for AI transcription
- **AI Transcription**: `WhisperTranscriptionService` integrates with OpenAI's Whisper API
- **Models**: 
  - `TranscriptEntry`: Individual transcript segment with text, start time, and duration
  - `TranscriptResponse`: API response wrapper with video metadata and transcript list

### API Details
- **Endpoint**: `GET /api/transcript/extract?url={youtube-url}`
- **Response Format**:
```json
{
  "videoId": "string",
  "title": "string",
  "transcript": [
    {
      "text": "string",
      "start": number,
      "duration": number
    }
  ],
  "success": boolean,
  "error": "string (if error)"
}
```

## Development Workflow

1. **Start Backend First**: The backend must be running on port 8080 before starting the frontend
2. **CORS**: Backend is configured to allow requests from http://localhost:3000
3. **Testing**: Both frontend and backend have test suites that should be run before committing changes

## Quick Setup

### Automated Setup (Recommended)
Run the setup script for your platform:

```bash
# Windows PowerShell (Recommended)
.\setup.ps1

# Windows Command Prompt  
setup.bat

# Linux/WSL
bash setup.sh
```

### Manual Setup

#### 1. Install Dependencies
```bash
# Install yt-dlp
pip install yt-dlp

# Install FFmpeg (Windows)
winget install FFmpeg
# Or download from: https://ffmpeg.org/download.html
```

#### 2. Configure OpenAI API Key
```bash
# Windows PowerShell
$env:OPENAI_API_KEY="your-api-key-here"

# Windows Command Prompt
set OPENAI_API_KEY=your-api-key-here
```
Get your API key from: https://platform.openai.com/api-keys

#### 3. Build and Run
```bash
cd transcript-api
./mvnw clean compile
./mvnw spring-boot:run
```

#### 4. Test Setup
```bash
# Check health
curl http://localhost:8080/api/health/status

# Test transcript extraction
curl "http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

### Without AI Transcription
- The application will fall back to YouTube's caption extraction methods
- Less reliable but doesn't require API keys or external dependencies

## Key Technologies

### Frontend
- React 19.1.0
- Tailwind CSS 3.4.17
- lucide-react for icons
- Create React App for build tooling

### Backend
- Spring Boot 3.2.0
- Java 17
- JSoup 1.17.2 for HTML parsing
- Apache HttpClient5 for HTTP requests
- Jackson for JSON processing
- Lombok for reducing boilerplate code