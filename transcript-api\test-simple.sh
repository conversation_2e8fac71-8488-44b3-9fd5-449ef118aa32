#!/bin/bash
echo "=== Testing yt-dlp ==="
./yt-dlp --version
echo ""

echo "=== Testing OpenAI API key ==="
if [ -f "../.env" ]; then
    source ../.env
    if [ ! -z "$OPENAI_API_KEY" ]; then
        echo "✓ OpenAI API key loaded"
    else
        echo "✗ OpenAI API key not found in .env"
    fi
else
    echo "✗ .env file not found"
fi
echo ""

echo "=== Testing Java ==="
if [ -f "/mnt/c/Program Files/Java/jdk-21/bin/java.exe" ]; then
    "/mnt/c/Program Files/Java/jdk-21/bin/java.exe" -version
    echo "✓ Java found"
else
    echo "✗ Java not found"
fi
echo ""

echo "=== Ready to run ==="
echo "To start the application:"
echo "1. Open PowerShell in Windows"
echo "2. cd C:\\Users\\<USER>\\Documents\\dev\\july\\youtube-transcribe\\transcript-api"
echo "3. .\\mvnw.cmd clean compile spring-boot:run"
echo ""
echo "Or use this command:"
echo "cmd.exe /c \"cd transcript-api && mvnw.cmd clean compile spring-boot:run\""