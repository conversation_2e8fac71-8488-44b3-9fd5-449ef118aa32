package com.youtube.transcript.service;

import com.youtube.transcript.model.TranscriptEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Service
public class SimpleAITranscriptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleAITranscriptionService.class);
    
    @Autowired
    private OpenAIWhisperClient whisperClient;
    
    @Value("${openai.api.key:}")
    private String openAiApiKey;
    
    public List<TranscriptEntry> transcribeAudio(File audioFile) throws Exception {
        logger.info("Starting AI transcription for file: {}", audioFile.getName());
        
        if (!whisperClient.isConfigured()) {
            throw new Exception("OpenAI API key not configured");
        }
        
        try {
            // Call OpenAI Whisper API
            WhisperTranscriptionResult result = whisperClient.transcribe(audioFile);
            
            // Convert to TranscriptEntry list
            return convertToTranscriptEntries(result);
            
        } catch (Exception e) {
            logger.error("AI transcription failed: {}", e.getMessage(), e);
            throw new Exception("AI transcription failed: " + e.getMessage());
        }
    }
    
    public boolean isConfigured() {
        return whisperClient.isConfigured();
    }
    
    private List<TranscriptEntry> convertToTranscriptEntries(WhisperTranscriptionResult result) {
        List<TranscriptEntry> entries = new ArrayList<>();
        
        if (result.getSegments() != null && !result.getSegments().isEmpty()) {
            // Use segments with timestamps if available
            logger.debug("Converting {} segments to transcript entries", result.getSegments().size());
            
            for (WhisperSegment segment : result.getSegments()) {
                TranscriptEntry entry = new TranscriptEntry();
                entry.setText(segment.getText().trim());
                entry.setStart(segment.getStart());
                entry.setDuration(segment.getEnd() - segment.getStart());
                
                if (!entry.getText().isEmpty()) {
                    entries.add(entry);
                }
            }
        } else if (result.getText() != null && !result.getText().trim().isEmpty()) {
            // Fallback: create single entry with entire text
            logger.debug("Creating single transcript entry from full text");
            
            TranscriptEntry entry = new TranscriptEntry();
            entry.setText(result.getText().trim());
            entry.setStart(0.0);
            entry.setDuration(result.getDuration());
            entries.add(entry);
        }
        
        logger.info("Created {} transcript entries from AI transcription", entries.size());
        return entries;
    }
}