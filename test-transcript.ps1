# Test transcript extraction directly
Write-Host "Testing yt-dlp wrapper..." -ForegroundColor Green
& .\transcript-api\yt-dlp.bat --version

Write-Host "`nTesting OpenAI API key..." -ForegroundColor Green
if (Test-Path ".env") {
    $env:OPENAI_API_KEY = (Get-Content ".env" | Where-Object { $_ -match "OPENAI_API_KEY" } | ForEach-Object { $_.Split('=')[1] })
    if ($env:OPENAI_API_KEY) {
        Write-Host "✓ OpenAI API key loaded" -ForegroundColor Green
    } else {
        Write-Host "✗ OpenAI API key not found in .env" -ForegroundColor Red
    }
} else {
    Write-Host "✗ .env file not found" -ForegroundColor Red
}

Write-Host "`nStarting Spring Boot application..." -ForegroundColor Green
Write-Host "Use this command to start the application:" -ForegroundColor Cyan
Write-Host "cd transcript-api" -ForegroundColor Yellow
Write-Host ".\mvnw.cmd clean compile spring-boot:run" -ForegroundColor Yellow

Write-Host "`nOnce running, test with:" -ForegroundColor Cyan
Write-Host "curl http://localhost:8080/api/health/status" -ForegroundColor Yellow
Write-Host "curl \"http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ\"" -ForegroundColor Yellow