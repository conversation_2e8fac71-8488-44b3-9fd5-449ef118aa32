package com.youtube.transcript.controller;

import com.youtube.transcript.service.TranscriptService;
import com.youtube.transcript.service.AudioExtractionService;
import com.youtube.transcript.service.SimpleAITranscriptionService;
import com.youtube.transcript.model.TranscriptResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/debug")
public class DebugTranscriptController {
    
    @Autowired
    private TranscriptService transcriptService;
    
    @Autowired
    private AudioExtractionService audioService;
    
    @Autowired
    private SimpleAITranscriptionService aiService;
    
    @GetMapping("/test-audio")
    public ResponseEntity<Map<String, Object>> testAudioExtraction(@RequestParam String videoId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("videoId", videoId);
            result.put("aiConfigured", aiService.isConfigured());
            
            // Test audio extraction
            java.io.File audioFile = audioService.extractAudioFromYouTube(videoId);
            result.put("audioExtracted", true);
            result.put("audioFile", audioFile.getAbsolutePath());
            result.put("audioFileSize", audioFile.length());
            
            // Clean up
            audioService.cleanupTempFile(audioFile);
            result.put("success", true);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
        }
        
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/test-ai")
    public ResponseEntity<Map<String, Object>> testAI(@RequestParam String videoId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("videoId", videoId);
            result.put("aiConfigured", aiService.isConfigured());
            
            if (!aiService.isConfigured()) {
                result.put("success", false);
                result.put("error", "AI service not configured");
                return ResponseEntity.ok(result);
            }
            
            // Test full AI transcription
            java.io.File audioFile = audioService.extractAudioFromYouTube(videoId);
            result.put("audioExtracted", true);
            result.put("audioFile", audioFile.getAbsolutePath());
            result.put("audioFileSize", audioFile.length());
            
            // Transcribe
            var transcript = aiService.transcribeAudio(audioFile);
            result.put("transcriptEntries", transcript.size());
            result.put("success", true);
            
            // Clean up
            audioService.cleanupTempFile(audioFile);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
        }
        
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/test-transcript")
    public ResponseEntity<Map<String, Object>> testTranscript(@RequestParam String url) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("url", url);
            
            // Test full transcript extraction
            TranscriptResponse response = transcriptService.extractTranscript(url);
            result.put("success", response.isSuccess());
            result.put("transcriptEntries", response.getTranscript().size());
            result.put("videoId", response.getVideoId());
            result.put("title", response.getTitle());
            
            if (response.getError() != null) {
                result.put("error", response.getError());
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("stackTrace", java.util.Arrays.toString(e.getStackTrace()));
        }
        
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/check-config")
    public ResponseEntity<Map<String, Object>> checkConfig() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("aiServiceConfigured", aiService.isConfigured());
        
        // Check environment variable
        String envApiKey = System.getenv("OPENAI_API_KEY");
        result.put("envApiKey", envApiKey != null ? envApiKey.substring(0, Math.min(envApiKey.length(), 20)) + "..." : "null");
        
        // Check system property
        String propApiKey = System.getProperty("openai.api.key");
        result.put("propApiKey", propApiKey != null ? propApiKey.substring(0, Math.min(propApiKey.length(), 20)) + "..." : "null");
        
        return ResponseEntity.ok(result);
    }
}