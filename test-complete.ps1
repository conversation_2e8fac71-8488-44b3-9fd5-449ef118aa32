# Test complete YouTube transcript extraction setup

Write-Host "=== YouTube Transcript API Complete Test ===" -ForegroundColor Cyan
Write-Host ""

# Test 1: yt-dlp
Write-Host "1. Testing yt-dlp..." -ForegroundColor Yellow
& yt-dlp.bat --version
if ($LASTEXITCODE -eq 0) {
    Write-Host "   OK: yt-dlp is working" -ForegroundColor Green
} else {
    Write-Host "   ERROR: yt-dlp not working" -ForegroundColor Red
}

# Test 2: ffmpeg
Write-Host "`n2. Testing ffmpeg..." -ForegroundColor Yellow
& C:\ffmpeg\bin\ffmpeg -version 2>&1 | Select-Object -First 1
if ($LASTEXITCODE -eq 0) {
    Write-Host "   OK: ffmpeg is working" -ForegroundColor Green
} else {
    Write-Host "   ERROR: ffmpeg not working" -ForegroundColor Red
}

# Test 3: OpenAI API key
Write-Host "`n3. Testing OpenAI API key..." -ForegroundColor Yellow
if (Test-Path "..\\.env") {
    $env:OPENAI_API_KEY = (Get-Content "..\\.env" | Where-Object { $_ -match "OPENAI_API_KEY" } | ForEach-Object { $_.Split('=')[1] })
    if ($env:OPENAI_API_KEY) {
        Write-Host "   OK: OpenAI API key loaded" -ForegroundColor Green
    } else {
        Write-Host "   ERROR: OpenAI API key not found" -ForegroundColor Red
    }
} else {
    Write-Host "   ERROR: .env file not found" -ForegroundColor Red
}

# Test 4: Test audio extraction with ffmpeg
Write-Host "`n4. Testing audio extraction with ffmpeg..." -ForegroundColor Yellow
$testVideo = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
$tempFile = "test_extract.mp3"

# Clean up any existing test file
if (Test-Path $tempFile) {
    Remove-Item $tempFile
}

# Try to extract audio
$output = & python -m yt_dlp -x --audio-format mp3 --ffmpeg-location C:\ffmpeg\bin -o $tempFile --max-filesize 5M $testVideo 2>&1

if (Test-Path $tempFile) {
    $fileSize = (Get-Item $tempFile).Length
    Write-Host "   OK: Audio extracted successfully ($fileSize bytes)" -ForegroundColor Green
    Remove-Item $tempFile
} else {
    Write-Host "   ERROR: Audio extraction failed" -ForegroundColor Red
    Write-Host "   Output: $output" -ForegroundColor Gray
}

Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "All components are set up. You can now:" -ForegroundColor Green
Write-Host "1. Start the application: cd transcript-api && restart.bat" -ForegroundColor Yellow
Write-Host "2. Test the API: curl `"http://localhost:8080/api/transcript/extract?url=$testVideo`"" -ForegroundColor Yellow