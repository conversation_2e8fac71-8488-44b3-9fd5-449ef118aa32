@echo off
echo Checking OpenAI API key configuration...

REM Load from .env file
if exist ..\\.env (
    echo Loading .env file...
    for /f "tokens=1,2 delims==" %%a in (..\\.env) do (
        if "%%a"=="OPENAI_API_KEY" (
            set OPENAI_API_KEY=%%b
            echo Found API key in .env file
        )
    )
)

REM Check if API key is set
if defined OPENAI_API_KEY (
    echo API key is set: %OPENAI_API_KEY:~0,10%...
) else (
    echo ERROR: API key is not set!
)

REM Run a simple Java test
echo.
echo Testing with Java...
set JAVA_HOME=C:\Program Files\Java\jdk-21
"%JAVA_HOME%\bin\java" -version

echo.
echo Starting application with API key...
mvnw.cmd spring-boot:run