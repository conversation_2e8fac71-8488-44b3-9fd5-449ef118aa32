import React, { useState } from 'react';
import { Download, <PERSON><PERSON>, Clock, Loader2, AlertCircle, CheckCircle } from 'lucide-react';

const TranscriptExtractor = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [transcript, setTranscript] = useState(null);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [showCopySuccess, setShowCopySuccess] = useState(false);

  const API_BASE_URL = 'http://localhost:8080/api';

  const extractTranscript = async () => {
    setError('');
    setTranscript(null);

    if (!url.trim()) {
      setError('Please enter a YouTube URL');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/transcript/extract?url=${encodeURIComponent(url)}`);
      const data = await response.json();

      console.log('API Response:', data); // Debug log
      console.log('Transcript entries:', data.transcript?.length || 0); // Debug log

      if (!response.ok) {
        throw new Error(data.error || 'Failed to extract transcript');
      }

      if (!data.transcript || data.transcript.length === 0) {
        setError('No transcript found for this video. The video may not have captions available.');
        return;
      }

      setTranscript(data);
    } catch (err) {
      console.error('Error extracting transcript:', err); // Debug log
      setError(err.message || 'Failed to connect to server. Make sure the Java backend is running on port 8080.');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const copyTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    navigator.clipboard.writeText(text).then(() => {
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 3000);
    });
  };

  const downloadTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${transcript.videoId}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      extractTranscript();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-4xl">
        <h1 className="text-4xl font-bold text-center text-gray-800 mb-2">
          YouTube Transcript Extractor
        </h1>
        <p className="text-center text-gray-600 mb-8">
          Extract transcripts from any YouTube video with Java & React
        </p>

        <div className="flex gap-3 mb-6">
          <input
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter YouTube URL (e.g., https://www.youtube.com/watch?v=...)"
            className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 transition-colors"
            disabled={loading}
          />
          <button
            onClick={extractTranscript}
            disabled={loading}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all disabled:opacity-60 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              'Extract'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        )}

        {transcript && (
          <div className="border-2 border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800">
                    {transcript.title || 'Transcript'}
                  </h2>
                  <p className="text-sm text-gray-600">
                    Video ID: {transcript.videoId} | Entries: {transcript.transcript?.length || 0}
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTimestamps(!showTimestamps)}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    {showTimestamps ? <Clock className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                    {showTimestamps ? 'Hide' : 'Show'} Timestamps
                  </button>
                  <button
                    onClick={copyTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Copy
                  </button>
                  <button
                    onClick={downloadTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Download
                  </button>
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto p-6 bg-gray-50">
              {transcript.transcript && transcript.transcript.length > 0 ? (
                transcript.transcript.map((entry, index) => (
                  <p key={index} className="mb-3 text-gray-700 leading-relaxed">
                    {showTimestamps && (
                      <span className="font-mono text-sm text-purple-600 mr-3">
                        [{formatTime(entry.start)}]
                      </span>
                    )}
                    {entry.text}
                  </p>
                ))
              ) : (
                <p className="text-gray-500 italic">No transcript entries found.</p>
              )}
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Make sure the Java backend is running on port 8080. 
            The backend will attempt to fetch transcripts from YouTube videos with available captions.
          </p>
        </div>
      </div>

      {showCopySuccess && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in">
          <CheckCircle className="w-5 h-5" />
          Transcript copied to clipboard!
        </div>
      )}

      <style jsx>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        .animate-slide-in {
          animation: slide-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default TranscriptExtractor;