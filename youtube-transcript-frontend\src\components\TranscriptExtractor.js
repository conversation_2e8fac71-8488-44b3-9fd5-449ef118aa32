import React, { useState } from 'react';
import { Download, Co<PERSON>, Clock, Loader2, AlertCircle, CheckCircle, X } from 'lucide-react';

const TranscriptExtractor = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [transcript, setTranscript] = useState(null);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [showCopySuccess, setShowCopySuccess] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [backendStatus, setBackendStatus] = useState('checking'); // 'checking', 'online', 'offline'
  const [showBackendOfflineNotification, setShowBackendOfflineNotification] = useState(false);

  const API_BASE_URL = 'http://localhost:8080/api';

  // Check backend health
  const checkBackendHealth = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health/status`, {
        method: 'GET',
        timeout: 5000 // 5 second timeout
      });

      if (response.ok) {
        setBackendStatus('online');
        setShowBackendOfflineNotification(false);
        return true;
      } else {
        setBackendStatus('offline');
        return false;
      }
    } catch (error) {
      console.error('Backend health check failed:', error);
      setBackendStatus('offline');
      return false;
    }
  };

  // Check backend health on component mount and periodically
  React.useEffect(() => {
    checkBackendHealth();

    // Check every 30 seconds
    const healthCheckInterval = setInterval(checkBackendHealth, 30000);

    return () => clearInterval(healthCheckInterval);
  }, []);

  const extractTranscript = async () => {
    setError('');
    setTranscript(null);
    setShowSuccessNotification(false);
    setShowErrorNotification(false);

    if (!url.trim()) {
      setError('Please enter a YouTube URL');
      setShowErrorNotification(true);
      setTimeout(() => setShowErrorNotification(false), 5000);
      return;
    }

    // Check backend health before proceeding
    const isBackendOnline = await checkBackendHealth();
    if (!isBackendOnline) {
      setError('Backend server is not running. Please start the Java backend on port 8080.');
      setShowErrorNotification(true);
      setShowBackendOfflineNotification(true);
      setTimeout(() => setShowErrorNotification(false), 8000);
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/transcript/extract?url=${encodeURIComponent(url)}`);
      const data = await response.json();

      console.log('API Response:', data); // Debug log
      console.log('Transcript entries:', data.transcript?.length || 0); // Debug log

      if (!response.ok) {
        throw new Error(data.error || 'Failed to extract transcript');
      }

      if (!data.transcript || data.transcript.length === 0) {
        const errorMsg = data.error || 'No transcript found for this video. The video may not have captions available.';
        setError(errorMsg);
        console.log('Full API response:', data); // Debug log
        setShowErrorNotification(true);
        setTimeout(() => setShowErrorNotification(false), 5000);
        return;
      }

      setTranscript(data);
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 5000);
    } catch (err) {
      console.error('Error extracting transcript:', err); // Debug log

      // Check if it's a connection error
      if (err.name === 'TypeError' || err.message.includes('fetch') || err.message.includes('NetworkError')) {
        setError('Cannot connect to backend server. Please make sure the Java backend is running on port 8080.');
        setShowBackendOfflineNotification(true);
        setBackendStatus('offline');
      } else {
        setError(err.message || 'Failed to extract transcript. Please try again.');
      }

      setShowErrorNotification(true);
      setTimeout(() => setShowErrorNotification(false), 8000);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const copyTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    navigator.clipboard.writeText(text).then(() => {
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 3000);
    });
  };

  const downloadTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${transcript.videoId}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      extractTranscript();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            YouTube Transcript Extractor
          </h1>
          <p className="text-gray-600 mb-4">
            Extract transcripts from any YouTube video with Java & React
          </p>

          {/* Backend Status Indicator */}
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              backendStatus === 'online' ? 'bg-green-500' :
              backendStatus === 'offline' ? 'bg-red-500' :
              'bg-yellow-500'
            }`}></div>
            <span className={
              backendStatus === 'online' ? 'text-green-600' :
              backendStatus === 'offline' ? 'text-red-600' :
              'text-yellow-600'
            }>
              Backend: {
                backendStatus === 'online' ? 'Connected' :
                backendStatus === 'offline' ? 'Disconnected' :
                'Checking...'
              }
            </span>
          </div>
        </div>

        <div className="flex gap-3 mb-6">
          <input
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter YouTube URL (e.g., https://www.youtube.com/watch?v=...)"
            className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 transition-colors"
            disabled={loading}
          />
          <button
            onClick={extractTranscript}
            disabled={loading || backendStatus === 'offline'}
            className={`px-6 py-3 font-semibold rounded-lg transition-all ${
              backendStatus === 'offline'
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-lg transform hover:-translate-y-0.5'
            } ${(loading || backendStatus === 'offline') ? 'opacity-60 cursor-not-allowed transform-none' : ''}`}
            title={backendStatus === 'offline' ? 'Backend server is offline' : ''}
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : backendStatus === 'offline' ? (
              'Backend Offline'
            ) : (
              'Extract'
            )}
          </button>
        </div>

        {/* Loading Status */}
        {loading && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg mb-6 flex items-center gap-2">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Extracting transcript... This may take 30-60 seconds for AI transcription.</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        )}

        {transcript && (
          <div className="border-2 border-green-200 rounded-lg overflow-hidden">
            <div className="bg-green-50 px-6 py-4 border-b border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    {transcript.title || 'Transcript'}
                  </h2>
                  <p className="text-sm text-gray-600">
                    Video ID: {transcript.videoId} | Entries: {transcript.transcript?.length || 0}
                    {transcript.success && <span className="text-green-600 ml-2">✓ Successfully extracted</span>}
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTimestamps(!showTimestamps)}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    {showTimestamps ? <Clock className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                    {showTimestamps ? 'Hide' : 'Show'} Timestamps
                  </button>
                  <button
                    onClick={copyTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Copy
                  </button>
                  <button
                    onClick={downloadTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Download
                  </button>
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto p-6 bg-white">
              {transcript.transcript && transcript.transcript.length > 0 ? (
                <div className="space-y-4">
                  {transcript.transcript.map((entry, index) => (
                    <div key={index} className="group hover:bg-gray-50 p-3 rounded-lg transition-colors">
                      <div className="flex items-start gap-3">
                        {showTimestamps && (
                          <span className="font-mono text-sm text-purple-600 bg-purple-100 px-2 py-1 rounded whitespace-nowrap">
                            {formatTime(entry.start)}
                          </span>
                        )}
                        <p className="text-gray-700 leading-relaxed flex-1 text-base">
                          {entry.text}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 italic text-lg">No transcript entries found.</p>
                  <p className="text-gray-400 text-sm mt-2">
                    This video may not have captions available, or the extraction failed.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Make sure the Java backend is running on port 8080. 
            The backend will attempt to fetch transcripts from YouTube videos with available captions.
          </p>
        </div>
      </div>

      {/* Success Notification */}
      {showSuccessNotification && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <CheckCircle className="w-5 h-5" />
          <span>Transcript extracted successfully!</span>
          <button
            onClick={() => setShowSuccessNotification(false)}
            className="ml-2 hover:bg-green-700 rounded p-1"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Backend Offline Notification */}
      {showBackendOfflineNotification && (
        <div className="fixed top-4 right-4 bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50 max-w-md">
          <AlertCircle className="w-5 h-5 flex-shrink-0" />
          <div className="flex-1">
            <div className="font-medium">Backend Server Offline</div>
            <div className="text-sm opacity-90">Please start the Java backend on port 8080</div>
          </div>
          <button
            onClick={() => setShowBackendOfflineNotification(false)}
            className="ml-2 hover:bg-orange-700 rounded p-1 flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Error Notification */}
      {showErrorNotification && (
        <div className="fixed top-20 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <AlertCircle className="w-5 h-5" />
          <span>Failed to extract transcript</span>
          <button
            onClick={() => setShowErrorNotification(false)}
            className="ml-2 hover:bg-red-700 rounded p-1"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Copy Success Notification */}
      {showCopySuccess && (
        <div className="fixed top-16 right-4 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <CheckCircle className="w-5 h-5" />
          Transcript copied to clipboard!
        </div>
      )}

      <style jsx>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        .animate-slide-in {
          animation: slide-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default TranscriptExtractor;