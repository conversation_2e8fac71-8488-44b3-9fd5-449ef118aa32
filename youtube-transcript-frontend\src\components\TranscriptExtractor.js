import React, { useState } from 'react';
import { Download, Co<PERSON>, Clock, Loader2, AlertCircle, CheckCircle, X, FileText } from 'lucide-react';

const TranscriptExtractor = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [transcript, setTranscript] = useState(null);
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [showCopySuccess, setShowCopySuccess] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [backendStatus, setBackendStatus] = useState('checking'); // 'checking', 'online', 'offline'
  const [showBackendOfflineNotification, setShowBackendOfflineNotification] = useState(false);
  const [generatedQuestions, setGeneratedQuestions] = useState([]);
  const [selectedQuestions, setSelectedQuestions] = useState(new Set());
  const [showQuestions, setShowQuestions] = useState(false);

  const API_BASE_URL = 'http://localhost:8080/api';

  // Check backend health
  const checkBackendHealth = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health/status`, {
        method: 'GET',
        timeout: 5000 // 5 second timeout
      });

      if (response.ok) {
        setBackendStatus('online');
        setShowBackendOfflineNotification(false);
        return true;
      } else {
        setBackendStatus('offline');
        return false;
      }
    } catch (error) {
      console.error('Backend health check failed:', error);
      setBackendStatus('offline');
      return false;
    }
  };

  // Check backend health on component mount and periodically
  React.useEffect(() => {
    checkBackendHealth();

    // Check every 30 seconds
    const healthCheckInterval = setInterval(checkBackendHealth, 30000);

    return () => clearInterval(healthCheckInterval);
  }, []);

  const extractTranscript = async () => {
    setError('');
    setTranscript(null);
    setShowSuccessNotification(false);
    setShowErrorNotification(false);
    setGeneratedQuestions([]);
    setSelectedQuestions(new Set());
    setShowQuestions(false);

    if (!url.trim()) {
      setError('Please enter a YouTube URL');
      setShowErrorNotification(true);
      setTimeout(() => setShowErrorNotification(false), 5000);
      return;
    }

    // Check backend health before proceeding
    const isBackendOnline = await checkBackendHealth();
    if (!isBackendOnline) {
      setError('Backend server is not running. Please start the Java backend on port 8080.');
      setShowErrorNotification(true);
      setShowBackendOfflineNotification(true);
      setTimeout(() => setShowErrorNotification(false), 8000);
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/transcript/extract?url=${encodeURIComponent(url)}`);
      const data = await response.json();

      console.log('API Response:', data); // Debug log
      console.log('Transcript entries:', data.transcript?.length || 0); // Debug log

      if (!response.ok) {
        throw new Error(data.error || 'Failed to extract transcript');
      }

      if (!data.transcript || data.transcript.length === 0) {
        const errorMsg = data.error || 'No transcript found for this video. The video may not have captions available.';
        setError(errorMsg);
        console.log('Full API response:', data); // Debug log
        setShowErrorNotification(true);
        setTimeout(() => setShowErrorNotification(false), 5000);
        return;
      }

      setTranscript(data);
      setShowSuccessNotification(true);
      setTimeout(() => setShowSuccessNotification(false), 5000);
    } catch (err) {
      console.error('Error extracting transcript:', err); // Debug log

      // Check if it's a connection error
      if (err.name === 'TypeError' || err.message.includes('fetch') || err.message.includes('NetworkError')) {
        setError('Cannot connect to backend server. Please make sure the Java backend is running on port 8080.');
        setShowBackendOfflineNotification(true);
        setBackendStatus('offline');
      } else {
        setError(err.message || 'Failed to extract transcript. Please try again.');
      }

      setShowErrorNotification(true);
      setTimeout(() => setShowErrorNotification(false), 8000);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const copyTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    navigator.clipboard.writeText(text).then(() => {
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 3000);
    });
  };

  const downloadTranscript = () => {
    if (!transcript) return;

    const text = transcript.transcript
      .map(entry => showTimestamps ? `[${formatTime(entry.start)}] ${entry.text}` : entry.text)
      .join('\n');

    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transcript-${transcript.videoId}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const generateInterviewQuestions = () => {
    if (!transcript || !transcript.transcript || transcript.transcript.length === 0) return;

    const questions = [];
    const entries = transcript.transcript;
    let fullTranscript = entries.map(e => e.text).join(' ');
    
    // Process transcript in meaningful chunks
    let i = 0;
    while (i < entries.length) {
      let chunk = '';
      let startTime = entries[i].start;
      let chunkSize = Math.min(5, entries.length - i); // 5 entries for more context
      
      for (let j = 0; j < chunkSize; j++) {
        if (i + j < entries.length) {
          chunk += entries[i + j].text + ' ';
        }
      }
      
      chunk = chunk.trim();
      
      // Skip very short chunks
      if (chunk.length < 50) {
        i += chunkSize;
        continue;
      }
      
      // Analyze chunk content to generate technical questions
      const lowerChunk = chunk.toLowerCase();
      
      // Generate different types of technical interview questions based on content
      const questionTemplates = [];
      
      // Concept explanation questions
      if (lowerChunk.includes('is') || lowerChunk.includes('are') || lowerChunk.includes('means')) {
        const concepts = extractKeyPhrases(chunk);
        if (concepts.length > 0) {
          questionTemplates.push({
            question: `Explain ${concepts[0]} and its significance`,
            type: 'conceptual'
          });
        }
      }
      
      // How/Why questions
      if (lowerChunk.includes('because') || lowerChunk.includes('since') || lowerChunk.includes('due to')) {
        questionTemplates.push({
          question: `Why ${extractMainIdea(chunk)}?`,
          type: 'reasoning'
        });
      }
      
      // Implementation questions
      if (lowerChunk.includes('implement') || lowerChunk.includes('create') || lowerChunk.includes('build') || 
          lowerChunk.includes('code') || lowerChunk.includes('function') || lowerChunk.includes('method')) {
        questionTemplates.push({
          question: `How would you implement ${extractTechnicalTerm(chunk)}?`,
          type: 'implementation'
        });
      }
      
      // Comparison questions
      if (lowerChunk.includes('versus') || lowerChunk.includes('vs') || lowerChunk.includes('compared to') || 
          lowerChunk.includes('difference')) {
        questionTemplates.push({
          question: `Compare and contrast ${extractComparisonTerms(chunk)}`,
          type: 'comparison'
        });
      }
      
      // Best practices questions
      if (lowerChunk.includes('should') || lowerChunk.includes('best practice') || lowerChunk.includes('recommend')) {
        questionTemplates.push({
          question: `What are the best practices for ${extractTopic(chunk)}?`,
          type: 'best-practices'
        });
      }
      
      // Problem-solving questions
      if (lowerChunk.includes('problem') || lowerChunk.includes('issue') || lowerChunk.includes('challenge') || 
          lowerChunk.includes('solve')) {
        questionTemplates.push({
          question: `How would you solve ${extractProblem(chunk)}?`,
          type: 'problem-solving'
        });
      }
      
      // Add generated questions to the list
      questionTemplates.forEach(template => {
        if (template.question && template.question.length > 10) {
          questions.push({
            id: `q_${questions.length}`,
            question: template.question,
            answer: chunk,
            type: template.type,
            timestamp: formatTime(startTime),
            startTime: startTime
          });
        }
      });
      
      i += Math.max(3, chunkSize - 2); // Overlap chunks for better context
    }
    
    // Remove duplicate or very similar questions
    const uniqueQuestions = removeSimilarQuestions(questions);
    
    setGeneratedQuestions(uniqueQuestions);
    setSelectedQuestions(new Set(uniqueQuestions.map(q => q.id)));
    setShowQuestions(true);
  };
  
  // Helper functions for question generation
  const extractKeyPhrases = (text) => {
    // Simple extraction of key technical terms
    const words = text.split(' ');
    const technicalTerms = words.filter(word => 
      word.length > 4 && 
      (word[0] === word[0].toUpperCase() || word.includes('()') || word.includes('.'))
    );
    return technicalTerms.slice(0, 2);
  };
  
  const extractMainIdea = (text) => {
    const sentences = text.split('.');
    if (sentences.length > 0) {
      return sentences[0].trim().toLowerCase().replace(/^(this|that|it)\s+/, '');
    }
    return 'this works';
  };
  
  const extractTechnicalTerm = (text) => {
    const techWords = text.match(/\b[A-Z][a-z]+(?:[A-Z][a-z]+)*\b|\b\w+\(\)|\b\w+\.\w+/g) || [];
    return techWords[0] || 'this functionality';
  };
  
  const extractComparisonTerms = (text) => {
    const vsMatch = text.match(/(\w+)\s+(?:versus|vs\.?|compared to)\s+(\w+)/i);
    if (vsMatch) {
      return `${vsMatch[1]} and ${vsMatch[2]}`;
    }
    return 'these approaches';
  };
  
  const extractTopic = (text) => {
    const words = text.split(' ').filter(w => w.length > 3);
    const nounish = words.find(w => !['should', 'would', 'could', 'must', 'will'].includes(w.toLowerCase()));
    return nounish || 'this';
  };
  
  const extractProblem = (text) => {
    const problemMatch = text.match(/(?:problem|issue|challenge)\s+(?:with|of|in)?\s+([^.]+)/i);
    if (problemMatch) {
      return problemMatch[1].trim();
    }
    return 'this challenge';
  };
  
  const removeSimilarQuestions = (questions) => {
    const unique = [];
    const seenQuestions = new Set();
    
    questions.forEach(q => {
      const normalized = q.question.toLowerCase().replace(/[^\w\s]/g, '');
      const key = normalized.split(' ').slice(0, 5).join(' ');
      
      if (!seenQuestions.has(key)) {
        seenQuestions.add(key);
        unique.push(q);
      }
    });
    
    return unique;
  };

  const createFlashcards = () => {
    if (selectedQuestions.size === 0) {
      alert('Please select at least one question to export');
      return;
    }

    // CSV header
    const csvHeader = 'question,answer,category,sub_category,level,additional_info';
    const csvRows = [csvHeader];

    // Process selected questions
    generatedQuestions.forEach(q => {
      if (selectedQuestions.has(q.id)) {
        const question = `"${q.question.replace(/"/g, '""')}"`;
        const answer = `"${q.answer.replace(/"/g, '""')}"`;
        const category = `"${(transcript.title || 'Technical Interview').replace(/"/g, '""')}"`;
        const subCategory = `"${q.type}"`;
        const level = 'new';
        const additionalInfo = `"Timestamp: ${q.timestamp}"`;
        
        csvRows.push(`${question},${answer},${category},${subCategory},${level},${additionalInfo}`);
      }
    });
    
    // Create and download CSV file
    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `interview-questions-${transcript.videoId}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      extractTranscript();
    }
  };
  
  const toggleQuestion = (questionId) => {
    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(questionId)) {
      newSelected.delete(questionId);
    } else {
      newSelected.add(questionId);
    }
    setSelectedQuestions(newSelected);
  };
  
  const selectAllQuestions = () => {
    setSelectedQuestions(new Set(generatedQuestions.map(q => q.id)));
  };
  
  const deselectAllQuestions = () => {
    setSelectedQuestions(new Set());
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            YouTube Transcript Extractor
          </h1>
          <p className="text-gray-600 mb-4">
            Extract transcripts from any YouTube video with Java & React
          </p>

          {/* Backend Status Indicator */}
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${
              backendStatus === 'online' ? 'bg-green-500' :
              backendStatus === 'offline' ? 'bg-red-500' :
              'bg-yellow-500'
            }`}></div>
            <span className={
              backendStatus === 'online' ? 'text-green-600' :
              backendStatus === 'offline' ? 'text-red-600' :
              'text-yellow-600'
            }>
              Backend: {
                backendStatus === 'online' ? 'Connected' :
                backendStatus === 'offline' ? 'Disconnected' :
                'Checking...'
              }
            </span>
          </div>
        </div>

        <div className="flex gap-3 mb-6">
          <input
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter YouTube URL (e.g., https://www.youtube.com/watch?v=...)"
            className="flex-1 px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-purple-500 transition-colors"
            disabled={loading}
          />
          <button
            onClick={extractTranscript}
            disabled={loading || backendStatus === 'offline'}
            className={`px-6 py-3 font-semibold rounded-lg transition-all ${
              backendStatus === 'offline'
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-lg transform hover:-translate-y-0.5'
            } ${(loading || backendStatus === 'offline') ? 'opacity-60 cursor-not-allowed transform-none' : ''}`}
            title={backendStatus === 'offline' ? 'Backend server is offline' : ''}
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : backendStatus === 'offline' ? (
              'Backend Offline'
            ) : (
              'Extract'
            )}
          </button>
        </div>

        {/* Loading Status */}
        {loading && (
          <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg mb-6 flex items-center gap-2">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Extracting transcript... This may take 30-60 seconds for AI transcription.</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 flex items-center gap-2">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        )}

        {/* Transcript Box - Always Visible */}
        <div className={`border-2 rounded-lg overflow-hidden transition-all duration-300 ${
          transcript ? 'border-green-200' : 'border-gray-200'
        }`}>
          <div className={`px-6 py-4 border-b transition-all duration-300 ${
            transcript ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                  {transcript ? (
                    <>
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      {transcript.title || 'Transcript'}
                    </>
                  ) : (
                    <>
                      <Clock className="w-5 h-5 text-gray-400" />
                      Transcript
                    </>
                  )}
                </h2>
                <p className="text-sm text-gray-600">
                  {transcript ? (
                    <>
                      Video ID: {transcript.videoId} | Entries: {transcript.transcript?.length || 0}
                      {transcript.success && <span className="text-green-600 ml-2">✓ Successfully extracted</span>}
                    </>
                  ) : (
                    'Enter a YouTube URL above and click Extract to see the transcript here'
                  )}
                </p>
              </div>

              {/* Action Buttons - Only show when transcript is available */}
              {transcript && (
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowTimestamps(!showTimestamps)}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Clock className="w-4 h-4" />
                    {showTimestamps ? 'Hide' : 'Show'} Timestamps
                  </button>
                  <button
                    onClick={copyTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Copy className="w-4 h-4" />
                    Copy
                  </button>
                  <button
                    onClick={downloadTranscript}
                    className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Download
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Transcript Content Area */}
          <div className="max-h-96 overflow-y-auto p-6 bg-white">
            {loading ? (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <Loader2 className="w-8 h-8 animate-spin mb-4" />
                <p className="text-lg font-medium mb-2">Extracting transcript...</p>
                <p className="text-sm text-center">
                  This may take 30-60 seconds for AI transcription.<br />
                  Please wait while we process the audio.
                </p>
              </div>
            ) : transcript && transcript.transcript && transcript.transcript.length > 0 ? (
              <div className="space-y-4">
                {transcript.transcript.map((entry, index) => (
                  <div key={index} className="group hover:bg-gray-50 p-3 rounded-lg transition-colors">
                    <div className="flex items-start gap-3">
                      {showTimestamps && (
                        <span className="font-mono text-sm text-purple-600 bg-purple-100 px-2 py-1 rounded whitespace-nowrap">
                          {formatTime(entry.start)}
                        </span>
                      )}
                      <p className="text-gray-700 leading-relaxed flex-1 text-base">
                        {entry.text}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : transcript && (!transcript.transcript || transcript.transcript.length === 0) ? (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <AlertCircle className="w-8 h-8 mb-4 text-red-400" />
                <p className="text-lg font-medium mb-2">No transcript found</p>
                <p className="text-sm text-center">
                  This video may not have captions available or may be restricted.
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-gray-400">
                <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-4">
                  <Clock className="w-8 h-8" />
                </div>
                <p className="text-lg font-medium mb-2">Ready for transcript</p>
                <p className="text-sm text-center">
                  Enter a YouTube URL above and click "Extract" to get started.<br />
                  The transcript will appear here with timestamps.
                </p>
              </div>
            )}
          </div>
          
          {/* Create Cards Button - Show only when transcript is available */}
          {transcript && transcript.transcript && transcript.transcript.length > 0 && !showQuestions && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <button
                onClick={generateInterviewQuestions}
                className="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-0.5 transition-all flex items-center justify-center gap-2"
              >
                <FileText className="w-5 h-5" />
                Generate Interview Questions
              </button>
              <p className="text-xs text-gray-600 text-center mt-2">
                Generate technical interview questions from the transcript
              </p>
            </div>
          )}
          
          {/* Questions Selection UI */}
          {showQuestions && generatedQuestions.length > 0 && (
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Generated Interview Questions ({selectedQuestions.size} of {generatedQuestions.length} selected)
                </h3>
                <div className="flex gap-2 mb-3">
                  <button
                    onClick={selectAllQuestions}
                    className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50"
                  >
                    Select All
                  </button>
                  <button
                    onClick={deselectAllQuestions}
                    className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50"
                  >
                    Deselect All
                  </button>
                  <button
                    onClick={() => setShowQuestions(false)}
                    className="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 ml-auto"
                  >
                    Cancel
                  </button>
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto mb-4 space-y-3">
                {generatedQuestions.map((q) => (
                  <div 
                    key={q.id} 
                    className="bg-white p-3 rounded-lg border border-gray-200 hover:border-purple-300 transition-colors"
                  >
                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedQuestions.has(q.id)}
                        onChange={() => toggleQuestion(q.id)}
                        className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <p className="font-medium text-gray-800">{q.question}</p>
                        <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
                          <span className="inline-flex items-center px-2 py-0.5 rounded bg-gray-100">
                            {q.type}
                          </span>
                          <span className="inline-flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {q.timestamp}
                          </span>
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
              
              <button
                onClick={createFlashcards}
                disabled={selectedQuestions.size === 0}
                className={`w-full px-6 py-3 font-semibold rounded-lg transition-all flex items-center justify-center gap-2 ${
                  selectedQuestions.size === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:shadow-lg transform hover:-translate-y-0.5'
                }`}
              >
                <Download className="w-5 h-5" />
                Export {selectedQuestions.size} Question{selectedQuestions.size !== 1 ? 's' : ''} to CSV
              </button>
            </div>
          )}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Make sure the Java backend is running on port 8080. 
            The backend will attempt to fetch transcripts from YouTube videos with available captions.
          </p>
        </div>
      </div>

      {/* Success Notification */}
      {showSuccessNotification && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <CheckCircle className="w-5 h-5" />
          <span>Transcript extracted successfully!</span>
          <button
            onClick={() => setShowSuccessNotification(false)}
            className="ml-2 hover:bg-green-700 rounded p-1"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Backend Offline Notification */}
      {showBackendOfflineNotification && (
        <div className="fixed top-4 right-4 bg-orange-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50 max-w-md">
          <AlertCircle className="w-5 h-5 flex-shrink-0" />
          <div className="flex-1">
            <div className="font-medium">Backend Server Offline</div>
            <div className="text-sm opacity-90">Please start the Java backend on port 8080</div>
          </div>
          <button
            onClick={() => setShowBackendOfflineNotification(false)}
            className="ml-2 hover:bg-orange-700 rounded p-1 flex-shrink-0"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Error Notification */}
      {showErrorNotification && (
        <div className="fixed top-20 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <AlertCircle className="w-5 h-5" />
          <span>Failed to extract transcript</span>
          <button
            onClick={() => setShowErrorNotification(false)}
            className="ml-2 hover:bg-red-700 rounded p-1"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Copy Success Notification */}
      {showCopySuccess && (
        <div className="fixed top-16 right-4 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-slide-in z-50">
          <CheckCircle className="w-5 h-5" />
          Transcript copied to clipboard!
        </div>
      )}

      <style jsx>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        .animate-slide-in {
          animation: slide-in 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default TranscriptExtractor;