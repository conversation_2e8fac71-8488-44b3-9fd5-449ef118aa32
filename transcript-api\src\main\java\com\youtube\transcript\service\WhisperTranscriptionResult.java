package com.youtube.transcript.service;

import java.util.ArrayList;
import java.util.List;

public class WhisperTranscriptionResult {
    private String text;
    private String language;
    private double duration;
    private List<WhisperSegment> segments = new ArrayList<>();
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public double getDuration() {
        return duration;
    }
    
    public void setDuration(double duration) {
        this.duration = duration;
    }
    
    public List<WhisperSegment> getSegments() {
        return segments;
    }
    
    public void setSegments(List<WhisperSegment> segments) {
        this.segments = segments;
    }
    
    public void addSegment(WhisperSegment segment) {
        this.segments.add(segment);
    }
}