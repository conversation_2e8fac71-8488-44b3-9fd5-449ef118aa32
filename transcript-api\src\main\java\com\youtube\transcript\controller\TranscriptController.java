
package com.youtube.transcript.controller;

import com.youtube.transcript.model.TranscriptResponse;
import com.youtube.transcript.service.TranscriptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/transcript")
public class TranscriptController {

    private static final Logger logger = LoggerFactory.getLogger(TranscriptController.class);
    
    @Autowired
    private TranscriptService transcriptService;

    @GetMapping("/extract")
    public ResponseEntity<?> extractTranscript(@RequestParam String url) {
        logger.info("Received transcript extraction request for URL: {}", url);
        
        try {
            TranscriptResponse response = transcriptService.extractTranscript(url);
            logger.info("Successfully extracted transcript for video: {}, entries count: {}", 
                       response.getVideoId(), 
                       response.getTranscript() != null ? response.getTranscript().size() : 0);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error extracting transcript for URL: {}", url, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(e.getMessage()));
        }
    }

    static class ErrorResponse {
        private String error;

        public ErrorResponse(String error) {
            this.error = error;
        }

        public String getError() {
            return error;
        }
    }
}