
package com.youtube.transcript.controller;

import com.youtube.transcript.model.TranscriptResponse;
import com.youtube.transcript.service.TranscriptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/transcript")
public class TranscriptController {

    private static final Logger logger = LoggerFactory.getLogger(TranscriptController.class);
    
    @Autowired
    private TranscriptService transcriptService;

    @GetMapping("/extract")
    public ResponseEntity<?> extractTranscript(@RequestParam String url) {
        logger.info("Received transcript extraction request for URL: {}", url);

        try {
            TranscriptResponse response = transcriptService.extractTranscript(url);
            logger.info("Successfully extracted transcript for video: {}, entries count: {}",
                       response.getVideoId(),
                       response.getTranscript() != null ? response.getTranscript().size() : 0);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error extracting transcript for URL: {}", url, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse(e.getMessage()));
        }
    }

    @GetMapping("/diagnose")
    public ResponseEntity<?> diagnoseVideo(@RequestParam String url) {
        logger.info("Received diagnostic request for URL: {}", url);

        Map<String, Object> diagnosis = new HashMap<>();

        try {
            String videoId = transcriptService.extractVideoId(url);
            diagnosis.put("videoId", videoId);
            diagnosis.put("url", url);

            // Test video accessibility
            try {
                String youtubeUrl = "https://www.youtube.com/watch?v=" + videoId;
                org.jsoup.nodes.Document doc = org.jsoup.Jsoup.connect(youtubeUrl)
                        .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                        .get();

                String title = doc.select("meta[property=og:title]").attr("content");
                diagnosis.put("title", title);
                diagnosis.put("accessible", true);

                // Check for age restriction
                boolean ageRestricted = doc.html().contains("age_gate") || doc.html().contains("age-gate");
                diagnosis.put("ageRestricted", ageRestricted);

                // Check for captions
                boolean hasCaptions = doc.html().contains("captionTracks");
                diagnosis.put("hasCaptions", hasCaptions);

            } catch (Exception e) {
                diagnosis.put("accessible", false);
                diagnosis.put("accessError", e.getMessage());
            }

            return ResponseEntity.ok(diagnosis);

        } catch (Exception e) {
            logger.error("Error diagnosing video: {}", e.getMessage(), e);
            diagnosis.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(diagnosis);
        }
    }

    static class ErrorResponse {
        private String error;

        public ErrorResponse(String error) {
            this.error = error;
        }

        public String getError() {
            return error;
        }
    }
}