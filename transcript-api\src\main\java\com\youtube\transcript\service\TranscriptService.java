package com.youtube.transcript.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youtube.transcript.model.TranscriptEntry;
import com.youtube.transcript.model.TranscriptResponse;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

@Service
public class TranscriptService {

    private static final Logger logger = LoggerFactory.getLogger(TranscriptService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient httpClient = HttpClient.newHttpClient();
    
    @Autowired
    private YouTubeTranscriptExtractor innerTubeExtractor;
    
    @Autowired
    private SimpleTranscriptExtractor simpleExtractor;
    
    @Autowired
    private AudioExtractionService audioExtractionService;
    
    @Autowired
    private SimpleAITranscriptionService aiService;

    public TranscriptResponse extractTranscript(String url) throws Exception {
        logger.info("Extracting transcript from URL: {}", url);
        
        String videoId = extractVideoId(url);
        if (videoId == null) {
            logger.error("Invalid YouTube URL: {}", url);
            throw new IllegalArgumentException("Invalid YouTube URL");
        }
        logger.info("Extracted video ID: {}", videoId);
        
        // Try AI transcription first if configured
        if (aiService.isConfigured()) {
            logger.info("Trying AI transcription");
            try {
                List<TranscriptEntry> entries = transcribeWithAI(videoId);
                logger.info("AI transcription returned {} entries", entries != null ? entries.size() : 0);

                if (entries != null && !entries.isEmpty()) {
                    logger.info("Successfully transcribed {} entries using AI", entries.size());

                    // Get title
                    String title = "YouTube Video";
                    try {
                        String youtubeUrl = "https://www.youtube.com/watch?v=" + videoId;
                        Document doc = Jsoup.connect(youtubeUrl)
                                .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                                .get();
                        title = doc.select("meta[property=og:title]").attr("content");
                    } catch (Exception e) {
                        logger.warn("Failed to fetch title: {}", e.getMessage());
                    }

                    TranscriptResponse response = new TranscriptResponse();
                    response.setVideoId(videoId);
                    response.setTitle(title);
                    response.setTranscript(entries);
                    response.setSuccess(true);

                    logger.info("Returning AI transcription response with {} entries", response.getTranscript().size());
                    return response;
                } else {
                    logger.warn("AI transcription returned empty or null entries list");
                }
            } catch (Exception aiError) {
                logger.error("AI transcription failed with exception: {}", aiError.getMessage(), aiError);
            }
        } else {
            logger.info("OpenAI API key not configured, skipping AI transcription");
        }
        
        // Try direct timedtext API as backup
        logger.info("Trying direct timedtext API as backup");
        try {
            List<TranscriptEntry> entries = fetchTranscriptDirectly(videoId);
            if (!entries.isEmpty()) {
                logger.info("Successfully extracted {} entries using direct API", entries.size());
                
                // Get title
                String title = "YouTube Video";
                try {
                    String youtubeUrl = "https://www.youtube.com/watch?v=" + videoId;
                    Document doc = Jsoup.connect(youtubeUrl)
                            .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                            .get();
                    title = doc.select("meta[property=og:title]").attr("content");
                } catch (Exception e) {
                    logger.warn("Failed to fetch title: {}", e.getMessage());
                }
                
                TranscriptResponse response = new TranscriptResponse();
                response.setVideoId(videoId);
                response.setTitle(title);
                response.setTranscript(entries);
                response.setSuccess(true);
                
                return response;
            }
        } catch (Exception directError) {
            logger.warn("Direct API failed, trying other methods: {}", directError.getMessage());
        }
        
        // Try the simple extractor as backup
        logger.info("Trying simple extractor as backup");
        try {
            List<TranscriptEntry> entries = simpleExtractor.extractTranscript(videoId);
            if (!entries.isEmpty()) {
                logger.info("Successfully extracted {} entries using simple extractor", entries.size());
                
                String title = "YouTube Video";
                try {
                    String youtubeUrl = "https://www.youtube.com/watch?v=" + videoId;
                    Document doc = Jsoup.connect(youtubeUrl)
                            .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                            .get();
                    title = doc.select("meta[property=og:title]").attr("content");
                } catch (Exception e) {
                    logger.warn("Failed to fetch title: {}", e.getMessage());
                }
                
                TranscriptResponse response = new TranscriptResponse();
                response.setVideoId(videoId);
                response.setTitle(title);
                response.setTranscript(entries);
                response.setSuccess(true);
                
                return response;
            }
        } catch (Exception simpleError) {
            logger.warn("Simple extractor failed, trying other methods: {}", simpleError.getMessage());
        }

        // Fetch the YouTube page (fallback method)
        String youtubeUrl = "https://www.youtube.com/watch?v=" + videoId;
        logger.info("Fetching YouTube page for fallback methods: {}", youtubeUrl);
        
        Document doc = Jsoup.connect(youtubeUrl)
                .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .get();

        // Extract video title
        String title = doc.select("meta[property=og:title]").attr("content");
        logger.info("Video title: {}", title);

        // Try to extract transcript data from the page
        String html = doc.html();
        logger.debug("Page HTML length: {} characters", html.length());

        // Look for caption tracks in the HTML
        Pattern pattern = Pattern.compile("\"captionTracks\":\\[(.*?)\\]", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(html);

        if (matcher.find()) {
            logger.info("Found captionTracks in HTML");
            String captionData = matcher.group(1);
            logger.debug("Caption data: {}", captionData.substring(0, Math.min(captionData.length(), 200)) + "...");

            // Try to find English captions first, then any available caption
            List<String> captionUrls = extractCaptionUrls(captionData);
            
            // Also try constructing direct URLs for common cases
            captionUrls.add(String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en&fmt=srv3", videoId));
            captionUrls.add(String.format("https://www.youtube.com/api/timedtext?v=%s&lang=en-US&fmt=srv3", videoId));
            
            for (String captionUrl : captionUrls) {
                logger.info("Trying caption URL: {}", captionUrl);
                
                try {
                    List<TranscriptEntry> entries = fetchTranscriptFromUrl(captionUrl);
                    
                    if (entries != null && !entries.isEmpty()) {
                        logger.info("Successfully fetched {} transcript entries", entries.size());
                        
                        TranscriptResponse response = new TranscriptResponse();
                        response.setVideoId(videoId);
                        response.setTitle(title);
                        response.setTranscript(entries);
                        response.setSuccess(true);
                        
                        return response;
                    } else {
                        logger.warn("Caption URL returned empty transcript");
                    }
                } catch (Exception e) {
                    logger.warn("Failed to fetch transcript from URL: {}", e.getMessage());
                }
            }
            
            logger.warn("All caption URLs failed or returned empty transcripts");
        } else {
            logger.warn("No captionTracks found in HTML");
        }

        // Alternative method: Use YouTube's timedtext API
        logger.info("Trying alternative method: timedtext API");
        
        try {
            List<TranscriptEntry> entries = fetchTranscriptFromTimedText(videoId);
            logger.info("Fetched {} transcript entries from timedtext API", entries.size());

            TranscriptResponse response = new TranscriptResponse();
            response.setVideoId(videoId);
            response.setTitle(title);
            response.setTranscript(entries);
            response.setSuccess(true);

            return response;
        } catch (Exception e) {
            logger.error("Timedtext API failed: {}", e.getMessage());
            
            // Try the simple extractor as a fallback
            logger.info("Trying simple extractor as fallback");
            try {
                List<TranscriptEntry> entries = simpleExtractor.extractTranscript(videoId);
                if (!entries.isEmpty()) {
                    logger.info("Successfully extracted {} entries using simple extractor", entries.size());
                    
                    TranscriptResponse response = new TranscriptResponse();
                    response.setVideoId(videoId);
                    response.setTitle(title);
                    response.setTranscript(entries);
                    response.setSuccess(true);
                    
                    return response;
                }
            } catch (Exception simpleError) {
                logger.error("Simple extractor also failed: {}", simpleError.getMessage());
            }
            
            // Try the InnerTube API as a final fallback
            logger.info("Trying InnerTube API as final fallback");
            try {
                List<TranscriptEntry> entries = innerTubeExtractor.extractUsingInnerTube(videoId);
                if (!entries.isEmpty()) {
                    logger.info("Successfully extracted {} entries using InnerTube API", entries.size());
                    
                    TranscriptResponse response = new TranscriptResponse();
                    response.setVideoId(videoId);
                    response.setTitle(title);
                    response.setTranscript(entries);
                    response.setSuccess(true);
                    
                    return response;
                }
            } catch (Exception innerTubeError) {
                logger.error("InnerTube API also failed: {}", innerTubeError.getMessage());
            }
            
            // If all methods fail, return empty transcript
            TranscriptResponse response = new TranscriptResponse();
            response.setVideoId(videoId);
            response.setTitle(title);
            response.setTranscript(new ArrayList<>());
            response.setSuccess(false);
            response.setError("No transcript available for this video. The video may not have captions enabled or may be restricted.");
            
            return response;
        }
    }

    private List<TranscriptEntry> fetchTranscriptFromUrl(String url) throws Exception {
        logger.debug("Fetching transcript from URL: {}", url);
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                .header("Accept", "*/*")
                .header("Accept-Language", "en-US,en;q=0.9")
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        logger.debug("Response status: {}, body length: {}", response.statusCode(), response.body().length());
        
        if (response.statusCode() != 200) {
            logger.error("Failed to fetch transcript, status: {}", response.statusCode());
            throw new Exception("Failed to fetch transcript: HTTP " + response.statusCode());
        }
        
        if (response.body().isEmpty()) {
            logger.warn("Empty response body from caption URL");
            return new ArrayList<>();
        }

        // Try parsing as XML first
        if (response.body().trim().startsWith("<")) {
            return parseTranscriptXml(response.body());
        } else {
            // Try as JSON if not XML
            return parseTranscriptJson(response.body());
        }
    }

    private List<TranscriptEntry> fetchTranscriptFromTimedText(String videoId) throws Exception {
        // Try multiple formats and languages
        String[] languages = {"en", "en-US", "a.en"}; // a.en is auto-generated English
        String[] formats = {"json3", "srv3", "srv1"};
        
        for (String lang : languages) {
            for (String fmt : formats) {
                String url = String.format(
                        "https://www.youtube.com/api/timedtext?v=%s&lang=%s&fmt=%s",
                        videoId, lang, fmt
                );
                logger.debug("Trying timedtext API: {}", url);

                try {
                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(url))
                            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
                            .header("Accept", "*/*")
                            .header("Accept-Language", "en-US,en;q=0.9")
                            .build();

                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                    logger.debug("Response status: {}, body length: {}", response.statusCode(), response.body().length());

                    if (response.statusCode() == 200 && !response.body().isEmpty()) {
                        List<TranscriptEntry> entries;
                        
                        if (fmt.equals("json3")) {
                            entries = parseTranscriptJson(response.body());
                        } else {
                            entries = parseTranscriptXml(response.body());
                        }
                        
                        if (!entries.isEmpty()) {
                            logger.info("Successfully fetched {} entries using lang={}, fmt={}", entries.size(), lang, fmt);
                            return entries;
                        }
                    }
                } catch (Exception e) {
                    logger.debug("Failed with lang={}, fmt={}: {}", lang, fmt, e.getMessage());
                }
            }
        }

        logger.error("All timedtext API attempts failed");
        throw new Exception("No transcript available for this video");
    }

    private List<TranscriptEntry> parseTranscriptXml(String xml) {
        logger.debug("Parsing XML transcript, length: {}", xml.length());
        List<TranscriptEntry> entries = new ArrayList<>();
        
        if (xml.trim().isEmpty()) {
            logger.warn("Empty XML content");
            return entries;
        }
        
        Document doc = Jsoup.parse(xml, "", org.jsoup.parser.Parser.xmlParser());

        doc.select("text").forEach(element -> {
            try {
                TranscriptEntry entry = new TranscriptEntry();
                entry.setText(element.text());
                
                String startStr = element.attr("start");
                String durStr = element.attr("dur");
                
                if (!startStr.isEmpty()) {
                    entry.setStart(Double.parseDouble(startStr));
                }
                if (!durStr.isEmpty()) {
                    entry.setDuration(Double.parseDouble(durStr));
                }
                
                entries.add(entry);
            } catch (Exception e) {
                logger.warn("Failed to parse XML entry: {}", e.getMessage());
            }
        });

        logger.debug("Parsed {} XML entries", entries.size());
        return entries;
    }

    private List<TranscriptEntry> parseTranscriptJson(String json) throws Exception {
        logger.debug("Parsing JSON transcript, length: {}", json.length());
        List<TranscriptEntry> entries = new ArrayList<>();
        JsonNode root = objectMapper.readTree(json);

        if (root.has("events")) {
            logger.debug("Found 'events' in JSON");
            root.get("events").forEach(event -> {
                if (event.has("segs")) {
                    StringBuilder text = new StringBuilder();
                    event.get("segs").forEach(seg -> {
                        if (seg.has("utf8")) {
                            text.append(seg.get("utf8").asText());
                        }
                    });

                    if (text.length() > 0) {
                        TranscriptEntry entry = new TranscriptEntry();
                        entry.setText(text.toString().trim());
                        entry.setStart(event.get("tStartMs").asDouble() / 1000.0);
                        entry.setDuration(event.get("dDurationMs").asDouble() / 1000.0);
                        entries.add(entry);
                    }
                }
            });
        } else {
            logger.warn("No 'events' field found in JSON response");
        }

        logger.debug("Parsed {} JSON entries", entries.size());
        return entries;
    }

    private String extractVideoId(String url) {
        Pattern[] patterns = {
                Pattern.compile("(?:https?://)?(?:www\\.)?youtube\\.com/watch\\?v=([^&]+)"),
                Pattern.compile("(?:https?://)?youtu\\.be/([^?]+)"),
                Pattern.compile("(?:https?://)?(?:www\\.)?youtube\\.com/embed/([^?]+)")
        };

        for (Pattern pattern : patterns) {
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        return null;
    }
    
    private List<String> extractCaptionUrls(String captionData) {
        List<String> urls = new ArrayList<>();
        
        // Log the full caption data to understand the structure
        logger.debug("Full caption data: {}", captionData);
        
        // Try to parse as JSON array first
        try {
            // Parse the entire captionTracks array
            JsonNode tracks = objectMapper.readTree("[" + captionData + "]");
            
            for (JsonNode track : tracks) {
                String baseUrl = track.path("baseUrl").asText("");
                String langCode = track.path("languageCode").asText("");
                String vssId = track.path("vssId").asText("");
                String name = track.path("name").path("simpleText").asText("");
                boolean isTranslatable = track.path("isTranslatable").asBoolean(false);
                
                logger.info("Found caption track: lang={}, vssId={}, name={}, translatable={}", 
                           langCode, vssId, name, isTranslatable);
                
                if (!baseUrl.isEmpty()) {
                    String captionUrl = baseUrl
                            .replace("\\u0026", "&")
                            .replace("\\/", "/");
                    
                    // Prioritize English tracks
                    if (langCode.startsWith("en") || vssId.startsWith("a.en") || vssId.equals("en")) {
                        urls.add(0, captionUrl);
                        logger.info("Added English caption URL for lang={}", langCode);
                    } else {
                        urls.add(captionUrl);
                        logger.info("Added non-English caption URL for lang={}", langCode);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse caption tracks as JSON: {}", e.getMessage());
            
            // Fallback to regex parsing
            Pattern urlPattern = Pattern.compile("\"baseUrl\":\"(.*?)\"");
            Matcher urlMatcher = urlPattern.matcher(captionData);
            
            while (urlMatcher.find()) {
                String captionUrl = urlMatcher.group(1)
                        .replace("\\u0026", "&")
                        .replace("\\/", "/");
                urls.add(captionUrl);
                logger.info("Found caption URL via regex");
            }
        }
        
        logger.info("Found {} caption URLs total", urls.size());
        return urls;
    }
    
    private List<TranscriptEntry> fetchTranscriptDirectly(String videoId) throws Exception {
        // Try multiple direct API approaches
        String[] languages = {"en", "a.en", "en-US"};
        String[] formats = {"srv3", "srv1", "json3"};
        
        for (String lang : languages) {
            for (String fmt : formats) {
                String url = String.format(
                        "https://www.youtube.com/api/timedtext?v=%s&lang=%s&fmt=%s",
                        videoId, lang, fmt
                );
                
                logger.debug("Trying direct URL: {}", url);
                
                try {
                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(url))
                            .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                            .header("Accept", "*/*")
                            .header("Accept-Language", "en-US,en;q=0.9")
                            .header("Referer", "https://www.youtube.com/")
                            .build();
                    
                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                    
                    if (response.statusCode() == 200 && !response.body().isEmpty()) {
                        logger.info("Got transcript with lang={}, fmt={}, length={}", lang, fmt, response.body().length());
                        
                        List<TranscriptEntry> entries;
                        if (fmt.equals("json3")) {
                            entries = parseTranscriptJson(response.body());
                        } else {
                            entries = parseTranscriptXml(response.body());
                        }
                        
                        if (!entries.isEmpty()) {
                            return entries;
                        }
                    }
                } catch (Exception e) {
                    logger.debug("Failed lang={}, fmt={}: {}", lang, fmt, e.getMessage());
                }
            }
        }
        
        throw new Exception("No transcript found using direct API");
    }
    
    private List<TranscriptEntry> transcribeWithAI(String videoId) throws Exception {
        File audioFile = null;
        try {
            // Extract audio from YouTube video
            logger.info("Extracting audio for AI transcription");
            audioFile = audioExtractionService.extractAudioFromYouTube(videoId);

            // Transcribe with AI service
            logger.info("Transcribing audio with AI service");
            List<TranscriptEntry> result = aiService.transcribeAudio(audioFile);
            logger.info("AI service returned {} entries", result != null ? result.size() : 0);
            return result;

        } finally {
            // Clean up temporary audio file
            if (audioFile != null) {
                audioExtractionService.cleanupTempFile(audioFile);
            }
        }
    }
}
