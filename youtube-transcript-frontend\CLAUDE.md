# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React frontend application for extracting YouTube transcripts. It communicates with a Java backend API running on port 8080.

## Common Commands

- `npm start` - Start development server (http://localhost:3000)
- `npm test` - Run tests in watch mode
- `npm run build` - Create production build
- `npm test -- --coverage` - Run tests with coverage report
- `npm test -- --watchAll=false` - Run tests once without watching

## Architecture

The application follows a simple component-based architecture:
- **src/App.js** - Root component that renders TranscriptExtractor
- **src/components/TranscriptExtractor** - Main component handling transcript extraction logic
- **Styling**: Tailwind CSS with utility classes directly in components
- **State Management**: React hooks (useState) for local state
- **API Communication**: Fetch API to backend at http://localhost:8080/api/transcript/extract

## Key Dependencies

- React 19.1.0
- Tailwind CSS 3.4.17 (configured in tailwind.config.js)
- lucide-react for icons
- Testing: @testing-library/react, jest-dom

## Development Notes

1. **Backend Dependency**: Ensure Java backend is running on port 8080 before starting frontend
2. **API Endpoint**: GET request to `/api/transcript/extract?url={youtube-url}`
3. **Response Format**: Expects JSON with `transcript` string containing lines of text
4. **CORS**: Backend must allow frontend origin (http://localhost:3000)

## Testing Approach

Uses React Testing Library with Jest. Tests are co-located with components using `.test.js` extension.