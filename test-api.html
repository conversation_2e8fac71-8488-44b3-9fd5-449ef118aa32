<!DOCTYPE html>
<html>
<head>
    <title>Test YouTube Transcript API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .input-group { margin-bottom: 20px; }
        input { width: 70%; padding: 10px; border: 2px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 5px; }
        .transcript-entry { margin-bottom: 15px; padding: 10px; background: white; border-left: 4px solid #007bff; }
        .timestamp { color: #6c757d; font-family: monospace; margin-right: 10px; }
        .loading { color: #007bff; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; }
        .success { color: #155724; background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 YouTube Transcript API Test</h1>
        <p>Test the transcript extraction directly from the browser</p>

        <div class="input-group">
            <input type="text" id="videoUrl" placeholder="Enter YouTube URL" value="https://www.youtube.com/watch?v=dQw4w9WgXcQ">
            <button onclick="testHealth()">Test Health</button>
            <button onclick="extractTranscript()">Extract Transcript</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        async function testHealth() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="loading">Testing backend health...</div>';

            try {
                const response = await fetch('http://localhost:8080/api/health/status');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Backend is running!</div>
                    <div class="result">
                        <h3>Health Status:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Backend connection failed: ${error.message}</div>
                    <p>Make sure the Java backend is running on port 8080</p>
                `;
            }
        }

        async function extractTranscript() {
            const url = document.getElementById('videoUrl').value;
            const resultDiv = document.getElementById('result');

            if (!url.trim()) {
                resultDiv.innerHTML = '<div class="error">Please enter a YouTube URL</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="loading">🔄 Extracting transcript... This may take a minute for AI transcription.</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/transcript/extract?url=${encodeURIComponent(url)}`);
                const data = await response.json();

                console.log('API Response:', data);

                if (!response.ok) {
                    throw new Error(data.error || 'API request failed');
                }

                if (data.transcript && data.transcript.length > 0) {
                    const transcriptHtml = data.transcript.map((entry, index) => `
                        <div class="transcript-entry">
                            <span class="timestamp">[${formatTime(entry.start)}]</span>
                            ${entry.text}
                        </div>
                    `).join('');

                    resultDiv.innerHTML = `
                        <div class="success">✅ Transcript extracted successfully!</div>
                        <div class="result">
                            <h3>${data.title || 'Transcript'}</h3>
                            <p><strong>Video ID:</strong> ${data.videoId} | <strong>Entries:</strong> ${data.transcript.length}</p>
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${transcriptHtml}
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ No transcript found</div>
                        <div class="result">
                            <p><strong>Response:</strong></p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${error.message}</div>
                    <p>Check the browser console for more details</p>
                `;
                console.error('Error:', error);
            }
        }

        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        // Auto-test health on page load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>