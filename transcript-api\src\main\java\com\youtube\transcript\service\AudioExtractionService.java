package com.youtube.transcript.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AudioExtractionService {
    
    private static final Logger logger = LoggerFactory.getLogger(AudioExtractionService.class);
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
    
    public File extractAudioFromYouTube(String videoId) throws Exception {
        logger.info("Extracting audio for video ID: {}", videoId);
        
        // Create temporary file path
        String fileName = "youtube_audio_" + videoId + ".mp3";
        Path tempPath = Paths.get(TEMP_DIR, fileName);
        
        // Check if yt-dlp is available, if not try youtube-dl
        String[] commands = {
            "yt-dlp.bat",  // Use our wrapper script first
            "yt-dlp", 
            "youtube-dl"
        };
        
        Exception lastException = null;
        
        for (String command : commands) {
            try {
                if (isCommandAvailable(command)) {
                    return extractWithCommand(command, videoId, tempPath);
                }
            } catch (Exception e) {
                logger.warn("Failed with {}: {}", command, e.getMessage());
                lastException = e;
            }
        }
        
        // If both fail, provide helpful error message
        logger.error("No audio extraction tool available");
        throw new Exception("Audio extraction failed. Please install yt-dlp: pip install yt-dlp");
    }
    
    private boolean isCommandAvailable(String command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command, "--version");
            // Set working directory to project root for .bat file
            if (command.equals("yt-dlp.bat")) {
                pb.directory(new File(System.getProperty("user.dir")));
            }
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);
            return finished && process.exitValue() == 0;
        } catch (Exception e) {
            logger.debug("Command {} not available: {}", command, e.getMessage());
            return false;
        }
    }
    
    private File extractWithCommand(String command, String videoId, Path outputPath) throws Exception {
        logger.info("Using {} to extract audio", command);
        
        String url = "https://www.youtube.com/watch?v=" + videoId;
        
        List<String> commandArgs = new ArrayList<>();
        commandArgs.add(command);
        commandArgs.add("-x");  // Extract audio
        commandArgs.add("--audio-format");
        commandArgs.add("mp3");
        commandArgs.add("--audio-quality");
        commandArgs.add("5");  // Good quality but smaller file for API limits
        commandArgs.add("-o");
        commandArgs.add(outputPath.toString().replace(".mp3", ".%(ext)s"));
        commandArgs.add("--no-playlist");
        commandArgs.add("--max-filesize");
        commandArgs.add("50M");  // Increased limit to 50MB for longer videos
        commandArgs.add("--ffmpeg-location");
        commandArgs.add("C:\\ffmpeg\\bin");  // Specify ffmpeg location
        commandArgs.add(url);
        
        logger.debug("Executing command: {}", String.join(" ", commandArgs));
        
        ProcessBuilder pb = new ProcessBuilder(commandArgs);
        pb.redirectErrorStream(true);
        // Set working directory to project root for .bat file
        if (command.equals("yt-dlp.bat")) {
            pb.directory(new File(System.getProperty("user.dir")));
        }
        
        Process process = pb.start();
        
        // Read output
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                if (line.contains("ERROR") || line.contains("WARNING")) {
                    logger.warn("yt-dlp: {}", line);
                } else {
                    logger.debug("yt-dlp: {}", line);
                }
            }
        }
        
        boolean finished = process.waitFor(300, TimeUnit.SECONDS); // 5 minute timeout
        
        if (!finished) {
            process.destroyForcibly();
            throw new Exception("Audio extraction timed out after 5 minutes");
        }
        
        if (process.exitValue() != 0) {
            logger.error("Command failed with exit code: {}", process.exitValue());
            logger.error("Output: {}", output.toString());
            throw new Exception("Failed to extract audio: " + output.toString());
        }
        
        // Find the generated file
        File audioFile = findGeneratedAudioFile(outputPath);
        if (audioFile == null || !audioFile.exists()) {
            logger.error("Audio file not found. Command output: {}", output.toString());
            throw new Exception("Audio file was not created. Check yt-dlp installation and video availability.");
        }
        
        // Check file size
        long fileSizeMB = audioFile.length() / (1024 * 1024);
        logger.info("Successfully extracted audio: {} ({}MB)", audioFile.getAbsolutePath(), fileSizeMB);
        
        if (audioFile.length() > 25 * 1024 * 1024) {
            logger.warn("Audio file is {}MB, which exceeds OpenAI's 25MB limit", fileSizeMB);
        }
        
        return audioFile;
    }
    
    private File extractWithPython(String videoId, Path outputPath) throws Exception {
        logger.info("Trying Python-based extraction");
        
        // Create a Python script for yt-dlp
        String pythonScript = String.format("""
            import yt_dlp
            import sys
            
            try:
                ydl_opts = {
                    'format': 'bestaudio/best',
                    'outtmpl': '%s',
                    'postprocessors': [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'mp3',
                        'preferredquality': '192',
                    }],
                }
                
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    ydl.download(['https://www.youtube.com/watch?v=%s'])
                    
                print("SUCCESS")
            except Exception as e:
                print(f"ERROR: {e}")
                sys.exit(1)
            """, outputPath.toString().replace(".mp3", ".%(ext)s"), videoId);
        
        // Write Python script to temp file
        Path scriptPath = Paths.get(TEMP_DIR, "extract_audio.py");
        Files.writeString(scriptPath, pythonScript);
        
        try {
            ProcessBuilder pb = new ProcessBuilder("python", scriptPath.toString());
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    logger.debug("Python output: {}", line);
                }
            }
            
            boolean finished = process.waitFor(300, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                throw new Exception("Python extraction timed out");
            }
            
            if (process.exitValue() != 0) {
                throw new Exception("Python extraction failed: " + output.toString());
            }
            
            File audioFile = findGeneratedAudioFile(outputPath);
            if (audioFile == null || !audioFile.exists()) {
                throw new Exception("Audio file was not created by Python script");
            }
            
            return audioFile;
            
        } finally {
            // Clean up script file
            try {
                Files.deleteIfExists(scriptPath);
            } catch (IOException e) {
                logger.warn("Failed to delete temp script: {}", e.getMessage());
            }
        }
    }
    
    private File findGeneratedAudioFile(Path basePath) {
        String baseFileName = basePath.getFileName().toString().replace(".mp3", "");
        String directory = basePath.getParent().toString();
        
        // Common extensions yt-dlp might generate
        String[] extensions = {".mp3", ".m4a", ".webm", ".ogg", ".wav"};
        
        for (String ext : extensions) {
            File file = new File(directory, baseFileName + ext);
            if (file.exists()) {
                logger.debug("Found audio file: {}", file.getAbsolutePath());
                return file;
            }
        }
        
        // Also check for files with additional suffixes (yt-dlp sometimes adds extra info)
        File dir = new File(directory);
        if (dir.exists()) {
            File[] files = dir.listFiles((d, name) -> 
                name.startsWith(baseFileName) && 
                (name.endsWith(".mp3") || name.endsWith(".m4a") || 
                 name.endsWith(".webm") || name.endsWith(".ogg") || name.endsWith(".wav"))
            );
            if (files != null && files.length > 0) {
                logger.debug("Found audio file with suffix: {}", files[0].getAbsolutePath());
                return files[0]; // Return the first match
            }
        }
        
        logger.warn("No audio file found for base name: {}", baseFileName);
        return null;
    }
    
    public void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                Files.delete(tempFile.toPath());
                logger.debug("Cleaned up temp file: {}", tempFile.getAbsolutePath());
            } catch (IOException e) {
                logger.warn("Failed to delete temp file: {}", e.getMessage());
            }
        }
    }
}