@echo off
echo ==============================================
echo Starting YouTube Transcript API with Environment
echo ==============================================

REM Set Java environment
set JAVA_HOME=C:\Program Files\Java\jdk-21
set PATH=%JAVA_HOME%\bin;%PATH%

REM Load environment variables from .env file
echo Loading environment variables...
if exist ..\\.env (
    for /f "usebackq tokens=1,2 delims==" %%a in ("..\\.env") do (
        if "%%a"=="OPENAI_API_KEY" (
            set OPENAI_API_KEY=%%b
            echo Found OpenAI API key in .env file
        )
    )
) else (
    echo WARNING: .env file not found!
)

REM Verify API key is set
if defined OPENAI_API_KEY (
    echo OpenAI API key is configured: %OPENAI_API_KEY:~0,10%...
) else (
    echo ERROR: OpenAI API key is not set!
    echo Please create a .env file with OPENAI_API_KEY=your-key-here
    pause
    exit /b 1
)

REM Start the application with environment variables
echo.
echo Starting Spring Boot application...
echo.
mvnw.cmd clean compile spring-boot:run

pause