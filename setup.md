# YouTube Transcript API Setup Guide

## Quick Setup Steps

### 1. Install Dependencies

#### Install yt-dlp (Required for AI transcription)
```bash
# Option 1: Using pip
pip install yt-dlp

# Option 2: Using pip3 
pip3 install yt-dlp

# Option 3: Download binary (Windows)
# Download from: https://github.com/yt-dlp/yt-dlp/releases
# Place yt-dlp.exe in your PATH
```

#### Install FFmpeg (Required by yt-dlp)
```bash
# Windows (via Chocolatey)
choco install ffmpeg

# Windows (via winget)  
winget install FFmpeg

# Or download from: https://ffmpeg.org/download.html
```

### 2. Configure OpenAI API Key

#### Option A: Using .env file (Recommended)
The project already includes a `.env` file with your API key. No additional setup needed!

#### Option B: Set environment variable manually
```bash
# Windows PowerShell
$env:OPENAI_API_KEY="your-openai-api-key-here"

# Windows Command Prompt
set OPENAI_API_KEY=your-openai-api-key-here

# Linux/WSL
export OPENAI_API_KEY="your-openai-api-key-here"
```

#### Option C: Load from .env file manually
```powershell
# PowerShell
. .\load-env.ps1
```

Get your API key from: https://platform.openai.com/api-keys

### 3. Build and Run

#### Option A: Using Maven Wrapper (Recommended)
```bash
cd transcript-api
./mvnw clean compile
./mvnw spring-boot:run
```

#### Option B: Using System Maven
```bash
cd transcript-api
mvn clean compile  
mvn spring-boot:run
```

#### Option C: Using IDE
- Open the project in IntelliJ IDEA or VS Code
- Run `TranscriptApplication.java`

### 4. Test the Setup

Once running, test the health endpoint:
```bash
curl http://localhost:8080/api/health/status
```

Expected response:
```json
{
  "application": "YouTube Transcript API",
  "status": "running", 
  "aiTranscriptionConfigured": true,
  "ytDlpAvailable": true,
  "ready": true,
  "message": "AI transcription fully configured and ready"
}
```

### 5. Test Transcript Extraction

```bash
curl "http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

## Troubleshooting

### Java Issues
If you get "JAVA_HOME not defined":
```bash
# Find Java installation
which java

# Set JAVA_HOME (example for WSL)
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64

# Or for Windows
set JAVA_HOME=C:\Program Files\Java\jdk-17
```

### yt-dlp Issues
```bash
# Test yt-dlp installation
yt-dlp --version

# Test with a simple video
yt-dlp -x --audio-format mp3 "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
```

### OpenAI API Issues
- Verify your API key is correct
- Check you have credits available
- Test with a curl command:
```bash
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

## How It Works

1. **AI Transcription (Primary)**: 
   - Extracts audio using yt-dlp
   - Sends to OpenAI Whisper API
   - Returns high-quality transcript with timestamps

2. **Caption Extraction (Fallback)**:
   - Direct YouTube timedtext API
   - HTML caption track extraction 
   - InnerTube API

3. **Response Format**:
```json
{
  "videoId": "dQw4w9WgXcQ",
  "title": "Rick Astley - Never Gonna Give You Up",
  "transcript": [
    {
      "text": "We're no strangers to love",
      "start": 0.0,
      "duration": 2.5
    }
  ],
  "success": true
}
```

## API Endpoints

- `GET /api/transcript/extract?url={youtube-url}` - Extract transcript
- `GET /api/health/status` - Check system status
- `GET /api/debug/youtube-data?videoId={id}` - Debug YouTube data extraction
- `GET /api/test/direct-caption?videoId={id}` - Test direct caption extraction

## Configuration Options

Add to `application.properties`:
```properties
# OpenAI Configuration
openai.api.key=your-api-key-here
openai.api.timeout=300

# Logging
logging.level.com.youtube.transcript=DEBUG
```