package com.youtube.transcript.controller;

import com.youtube.transcript.service.SimpleAITranscriptionService;
import com.youtube.transcript.service.AudioExtractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    @Autowired
    private SimpleAITranscriptionService aiService;
    
    @Autowired
    private AudioExtractionService audioService;
    
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("application", "YouTube Transcript API");
        status.put("status", "running");
        status.put("timestamp", System.currentTimeMillis());
        
        // Check AI transcription configuration
        status.put("aiTranscriptionConfigured", aiService.isConfigured());
        
        // Check if yt-dlp is available
        boolean ytDlpAvailable = checkYtDlpAvailability();
        status.put("ytDlpAvailable", ytDlpAvailable);
        
        // Overall readiness
        boolean ready = aiService.isConfigured() && ytDlpAvailable;
        status.put("ready", ready);
        
        if (ready) {
            status.put("message", "AI transcription fully configured and ready");
        } else if (aiService.isConfigured()) {
            status.put("message", "AI configured but yt-dlp not available - will fallback to caption extraction");
        } else {
            status.put("message", "AI not configured - using caption extraction only");
        }
        
        return ResponseEntity.ok(status);
    }
    
    private boolean checkYtDlpAvailability() {
        String[] commands = {"yt-dlp.bat", "yt-dlp", "youtube-dl"};
        
        for (String command : commands) {
            try {
                ProcessBuilder pb = new ProcessBuilder(command, "--version");
                // Set working directory to project root for .bat file
                if (command.equals("yt-dlp.bat")) {
                    pb.directory(new java.io.File(System.getProperty("user.dir")));
                }
                Process process = pb.start();
                boolean finished = process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS);
                if (finished && process.exitValue() == 0) {
                    return true;
                }
            } catch (Exception e) {
                // Continue to next command
            }
        }
        return false;
    }
}