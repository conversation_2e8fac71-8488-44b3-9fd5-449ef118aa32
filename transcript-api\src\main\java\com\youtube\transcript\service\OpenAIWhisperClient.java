package com.youtube.transcript.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.time.Duration;
import java.util.UUID;

@Component
public class OpenAIWhisperClient {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAIWhisperClient.class);
    private static final String WHISPER_API_URL = "https://api.openai.com/v1/audio/transcriptions";
    
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    @Value("${openai.api.key:}")
    private String apiKey;
    
    @Value("${openai.api.timeout:300}")
    private int timeoutSeconds;
    
    public OpenAIWhisperClient() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    public WhisperTranscriptionResult transcribe(File audioFile) throws Exception {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new Exception("OpenAI API key not configured");
        }
        
        logger.info("Sending audio file to OpenAI Whisper API: {} ({}MB)", 
                   audioFile.getName(), audioFile.length() / (1024 * 1024));
        
        // Check file size
        if (audioFile.length() > 25 * 1024 * 1024) {
            throw new Exception("File too large. Maximum size is 25MB, got " + 
                              (audioFile.length() / 1024 / 1024) + "MB");
        }
        
        String boundary = "Boundary-" + UUID.randomUUID().toString();
        byte[] multipartData = createMultipartFormData(audioFile, boundary);
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(WHISPER_API_URL))
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "multipart/form-data; boundary=" + boundary)
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .POST(HttpRequest.BodyPublishers.ofByteArray(multipartData))
                .build();
        
        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            
            logger.debug("OpenAI API response status: {}", response.statusCode());
            
            if (response.statusCode() != 200) {
                logger.error("OpenAI API error: {} - {}", response.statusCode(), response.body());
                throw new Exception("OpenAI API error: " + response.statusCode() + " - " + response.body());
            }
            
            return parseResponse(response.body());
            
        } catch (IOException | InterruptedException e) {
            logger.error("Failed to call OpenAI API", e);
            throw new Exception("Failed to call OpenAI API: " + e.getMessage());
        }
    }
    
    private byte[] createMultipartFormData(File audioFile, String boundary) throws IOException {
        String fileName = audioFile.getName();
        String mimeType = getMimeType(fileName);
        
        StringBuilder sb = new StringBuilder();
        
        // Add model parameter
        sb.append("--").append(boundary).append("\r\n");
        sb.append("Content-Disposition: form-data; name=\"model\"\r\n\r\n");
        sb.append("whisper-1\r\n");
        
        // Add response_format parameter
        sb.append("--").append(boundary).append("\r\n");
        sb.append("Content-Disposition: form-data; name=\"response_format\"\r\n\r\n");
        sb.append("verbose_json\r\n");
        
        // Add language parameter (optional, helps with accuracy)
        sb.append("--").append(boundary).append("\r\n");
        sb.append("Content-Disposition: form-data; name=\"language\"\r\n\r\n");
        sb.append("en\r\n");
        
        // Add file parameter
        sb.append("--").append(boundary).append("\r\n");
        sb.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(fileName).append("\"\r\n");
        sb.append("Content-Type: ").append(mimeType).append("\r\n\r\n");
        
        byte[] header = sb.toString().getBytes();
        byte[] fileContent = Files.readAllBytes(audioFile.toPath());
        byte[] footer = ("\r\n--" + boundary + "--\r\n").getBytes();
        
        // Combine all parts
        byte[] result = new byte[header.length + fileContent.length + footer.length];
        System.arraycopy(header, 0, result, 0, header.length);
        System.arraycopy(fileContent, 0, result, header.length, fileContent.length);
        System.arraycopy(footer, 0, result, header.length + fileContent.length, footer.length);
        
        return result;
    }
    
    private String getMimeType(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".mp3")) return "audio/mpeg";
        if (lowerFileName.endsWith(".wav")) return "audio/wav";
        if (lowerFileName.endsWith(".m4a")) return "audio/mp4";
        if (lowerFileName.endsWith(".ogg")) return "audio/ogg";
        if (lowerFileName.endsWith(".webm")) return "audio/webm";
        return "audio/mpeg"; // default
    }
    
    private WhisperTranscriptionResult parseResponse(String responseBody) throws Exception {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            
            WhisperTranscriptionResult result = new WhisperTranscriptionResult();
            result.setText(root.path("text").asText(""));
            result.setLanguage(root.path("language").asText("en"));
            result.setDuration(root.path("duration").asDouble(0.0));
            
            // Parse segments if available
            JsonNode segments = root.path("segments");
            if (!segments.isMissingNode() && segments.isArray()) {
                for (JsonNode segment : segments) {
                    WhisperSegment seg = new WhisperSegment();
                    seg.setId(segment.path("id").asInt());
                    seg.setStart(segment.path("start").asDouble());
                    seg.setEnd(segment.path("end").asDouble());
                    seg.setText(segment.path("text").asText(""));
                    result.addSegment(seg);
                }
            }
            
            logger.info("Transcription completed: {} characters, {} segments", 
                       result.getText().length(), result.getSegments().size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Failed to parse OpenAI response: {}", responseBody, e);
            throw new Exception("Failed to parse OpenAI response: " + e.getMessage());
        }
    }
    
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty();
    }
}