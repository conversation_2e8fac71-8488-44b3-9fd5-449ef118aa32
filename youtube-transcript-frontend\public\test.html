<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>YouTube Transcript API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8080/api/transcript/extract?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ');
                const data = await response.json();
                
                console.log('Response:', data);
                
                resultDiv.innerHTML = `
                    <h2>Response Status: ${response.status}</h2>
                    <h3>Video ID: ${data.videoId}</h3>
                    <h3>Title: ${data.title}</h3>
                    <h3>Success: ${data.success}</h3>
                    <h3>Transcript Entries: ${data.transcript ? data.transcript.length : 0}</h3>
                    <h4>First few entries:</h4>
                    <pre>${JSON.stringify(data.transcript?.slice(0, 3), null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<h2>Error: ${error.message}</h2>`;
            }
        }
    </script>
</body>
</html>
