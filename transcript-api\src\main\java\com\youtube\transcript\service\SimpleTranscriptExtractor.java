package com.youtube.transcript.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youtube.transcript.model.TranscriptEntry;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class SimpleTranscriptExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleTranscriptExtractor.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient httpClient = HttpClient.newHttpClient();
    
    public List<TranscriptEntry> extractTranscript(String videoId) throws Exception {
        logger.info("Extracting transcript for video: {}", videoId);
        
        // Step 1: Get the YouTube page
        String html = getYouTubePageHtml(videoId);
        
        // Step 2: Extract player response
        String playerResponse = extractPlayerResponse(html);
        if (playerResponse == null) {
            throw new Exception("Could not extract player response");
        }
        
        // Step 3: Parse player response to get caption tracks
        JsonNode playerData = objectMapper.readTree(playerResponse);
        List<String> captionUrls = extractCaptionUrls(playerData);
        
        if (captionUrls.isEmpty()) {
            throw new Exception("No caption tracks found");
        }
        
        // Step 4: Try each caption URL
        for (String captionUrl : captionUrls) {
            try {
                List<TranscriptEntry> entries = fetchTranscriptFromUrl(captionUrl);
                if (!entries.isEmpty()) {
                    logger.info("Successfully extracted {} transcript entries", entries.size());
                    return entries;
                }
            } catch (Exception e) {
                logger.warn("Failed to fetch from URL: {}", e.getMessage());
            }
        }
        
        throw new Exception("All caption URLs failed");
    }
    
    private String getYouTubePageHtml(String videoId) throws Exception {
        String url = "https://www.youtube.com/watch?v=" + videoId;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8")
                .header("Accept-Language", "en-US,en;q=0.9")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("DNT", "1")
                .header("Upgrade-Insecure-Requests", "1")
                .header("Sec-Fetch-Dest", "document")
                .header("Sec-Fetch-Mode", "navigate")
                .header("Sec-Fetch-Site", "none")
                .header("Cache-Control", "max-age=0")
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new Exception("Failed to fetch YouTube page: " + response.statusCode());
        }
        
        return response.body();
    }
    
    private String extractPlayerResponse(String html) {
        // Try multiple patterns to find player response
        String[] patterns = {
            "var ytInitialPlayerResponse = (\\{.*?\\});",
            "ytInitialPlayerResponse\":(\\{.*?\\}),\"",
            "\"player\":(\\{.*?\\}),\"",
            "ytInitialPlayerResponse = (\\{.*?\\});"
        };
        
        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                logger.debug("Found player response using pattern: {}", patternStr);
                return matcher.group(1);
            }
        }
        
        return null;
    }
    
    private List<String> extractCaptionUrls(JsonNode playerData) {
        List<String> urls = new ArrayList<>();
        
        try {
            JsonNode captions = playerData.path("captions");
            if (captions.isMissingNode()) {
                logger.warn("No captions node found in player response");
                return urls;
            }
            
            JsonNode captionTracks = captions.path("playerCaptionsTracklistRenderer")
                    .path("captionTracks");
            
            if (captionTracks.isMissingNode() || !captionTracks.isArray()) {
                logger.warn("No caption tracks found");
                return urls;
            }
            
            logger.info("Found {} caption tracks", captionTracks.size());
            
            // Sort by language preference (English first)
            List<JsonNode> trackList = new ArrayList<>();
            captionTracks.forEach(trackList::add);
            
            trackList.sort((a, b) -> {
                String langA = a.path("languageCode").asText("");
                String langB = b.path("languageCode").asText("");
                
                if (langA.startsWith("en")) return -1;
                if (langB.startsWith("en")) return 1;
                return 0;
            });
            
            for (JsonNode track : trackList) {
                String baseUrl = track.path("baseUrl").asText("");
                String languageCode = track.path("languageCode").asText("");
                String name = track.path("name").path("simpleText").asText("");
                
                logger.info("Caption track: {} - {}", languageCode, name);
                
                if (!baseUrl.isEmpty()) {
                    urls.add(baseUrl);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error extracting caption URLs: {}", e.getMessage());
        }
        
        return urls;
    }
    
    private List<TranscriptEntry> fetchTranscriptFromUrl(String url) throws Exception {
        logger.debug("Fetching transcript from: {}", url);
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .header("Accept", "*/*")
                .header("Accept-Language", "en-US,en;q=0.9")
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new Exception("HTTP " + response.statusCode());
        }
        
        String content = response.body();
        if (content.isEmpty()) {
            throw new Exception("Empty response");
        }
        
        logger.debug("Response content length: {}", content.length());
        
        // Parse the XML transcript
        return parseTranscriptXml(content);
    }
    
    private List<TranscriptEntry> parseTranscriptXml(String xml) {
        List<TranscriptEntry> entries = new ArrayList<>();
        
        try {
            Document doc = Jsoup.parse(xml, "", org.jsoup.parser.Parser.xmlParser());
            
            doc.select("text").forEach(element -> {
                try {
                    TranscriptEntry entry = new TranscriptEntry();
                    entry.setText(element.text().trim());
                    
                    String startStr = element.attr("start");
                    String durStr = element.attr("dur");
                    
                    if (!startStr.isEmpty()) {
                        entry.setStart(Double.parseDouble(startStr));
                    }
                    if (!durStr.isEmpty()) {
                        entry.setDuration(Double.parseDouble(durStr));
                    }
                    
                    if (!entry.getText().isEmpty()) {
                        entries.add(entry);
                    }
                } catch (Exception e) {
                    logger.warn("Failed to parse transcript entry: {}", e.getMessage());
                }
            });
            
        } catch (Exception e) {
            logger.error("Failed to parse transcript XML: {}", e.getMessage());
        }
        
        return entries;
    }
}