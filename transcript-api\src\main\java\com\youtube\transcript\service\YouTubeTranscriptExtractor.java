package com.youtube.transcript.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.youtube.transcript.model.TranscriptEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class YouTubeTranscriptExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(YouTubeTranscriptExtractor.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final HttpClient httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(10))
            .build();
    
    public List<TranscriptEntry> extractUsingInnerTube(String videoId) throws Exception {
        logger.info("Attempting to extract transcript using InnerTube API for video: {}", videoId);
        
        // First, get the initial page to extract necessary data
        String videoUrl = "https://www.youtube.com/watch?v=" + videoId;
        HttpRequest pageRequest = HttpRequest.newBuilder()
                .uri(URI.create(videoUrl))
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
                .header("Accept-Language", "en-US,en;q=0.5")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("DNT", "1")
                .header("Upgrade-Insecure-Requests", "1")
                .timeout(Duration.ofSeconds(30))
                .build();
        
        HttpResponse<String> pageResponse = httpClient.send(pageRequest, HttpResponse.BodyHandlers.ofString());
        
        if (pageResponse.statusCode() != 200) {
            logger.error("Failed to fetch YouTube page: status {}", pageResponse.statusCode());
            throw new Exception("Failed to fetch YouTube page");
        }
        
        String html = pageResponse.body();
        
        // Extract API key and initial data
        String apiKey = extractApiKey(html);
        JsonNode initialData = extractInitialData(html);
        
        if (apiKey == null || initialData == null) {
            logger.error("Failed to extract API key or initial data");
            throw new Exception("Failed to extract necessary data from YouTube page");
        }
        
        // Get the transcript using the innertube API
        return fetchTranscriptFromInnerTube(videoId, apiKey, initialData);
    }
    
    private String extractApiKey(String html) {
        Pattern pattern = Pattern.compile("\"INNERTUBE_API_KEY\":\"([^\"]+)\"");
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            String apiKey = matcher.group(1);
            logger.debug("Extracted API key: {}", apiKey);
            return apiKey;
        }
        
        // Alternative pattern
        pattern = Pattern.compile("\"innertubeApiKey\":\"([^\"]+)\"");
        matcher = pattern.matcher(html);
        if (matcher.find()) {
            String apiKey = matcher.group(1);
            logger.debug("Extracted API key (alt): {}", apiKey);
            return apiKey;
        }
        
        return null;
    }
    
    private JsonNode extractInitialData(String html) {
        try {
            Pattern pattern = Pattern.compile("var ytInitialData = (\\{.*?\\});", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(html);
            if (matcher.find()) {
                String jsonStr = matcher.group(1);
                return objectMapper.readTree(jsonStr);
            }
            
            // Alternative pattern
            pattern = Pattern.compile("window\\[\"ytInitialData\"\\] = (\\{.*?\\});", Pattern.DOTALL);
            matcher = pattern.matcher(html);
            if (matcher.find()) {
                String jsonStr = matcher.group(1);
                return objectMapper.readTree(jsonStr);
            }
        } catch (Exception e) {
            logger.error("Failed to parse initial data: {}", e.getMessage());
        }
        return null;
    }
    
    private List<TranscriptEntry> fetchTranscriptFromInnerTube(String videoId, String apiKey, JsonNode initialData) throws Exception {
        // Extract click tracking params
        String clickTrackingParams = extractClickTrackingParams(initialData);
        
        // Build the request body for transcript
        ObjectMapper mapper = new ObjectMapper();
        
        var clientNode = mapper.createObjectNode();
        clientNode.put("clientName", "WEB");
        clientNode.put("clientVersion", "2.20240101.00.00");
        
        var clickTrackingNode = mapper.createObjectNode();
        clickTrackingNode.put("clickTrackingParams", clickTrackingParams);
        
        var contextNode = mapper.createObjectNode();
        contextNode.set("client", clientNode);
        contextNode.set("clickTracking", clickTrackingNode);
        
        var requestBody = mapper.createObjectNode();
        requestBody.put("videoId", videoId);
        requestBody.set("context", contextNode);
        
        String innerTubeUrl = "https://www.youtube.com/youtubei/v1/get_transcript?key=" + apiKey;
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(innerTubeUrl))
                .header("Content-Type", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Origin", "https://www.youtube.com")
                .header("Referer", "https://www.youtube.com/watch?v=" + videoId)
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                .timeout(Duration.ofSeconds(30))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            logger.error("InnerTube API request failed: status {}", response.statusCode());
            throw new Exception("Failed to fetch transcript from InnerTube API");
        }
        
        return parseInnerTubeResponse(response.body());
    }
    
    private String extractClickTrackingParams(JsonNode initialData) {
        try {
            // Navigate through the JSON to find click tracking params
            JsonNode contents = initialData.path("contents")
                    .path("twoColumnWatchNextResults")
                    .path("results")
                    .path("results")
                    .path("contents");
            
            for (JsonNode content : contents) {
                JsonNode params = content.path("videoPrimaryInfoRenderer")
                        .path("trackingParams");
                if (!params.isMissingNode()) {
                    return params.asText();
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to extract click tracking params: {}", e.getMessage());
        }
        
        // Return a default value if not found
        return "CAAQu2kiEwjH8_2X0oSEAxWHFRsKHSULDJ8=";
    }
    
    private List<TranscriptEntry> parseInnerTubeResponse(String responseBody) throws Exception {
        List<TranscriptEntry> entries = new ArrayList<>();
        JsonNode root = objectMapper.readTree(responseBody);
        
        // Navigate to the transcript data
        JsonNode transcriptData = root.path("actions")
                .get(0)
                .path("updateEngagementPanelAction")
                .path("content")
                .path("transcriptRenderer")
                .path("body")
                .path("transcriptBodyRenderer")
                .path("cueGroups");
        
        if (transcriptData.isMissingNode() || !transcriptData.isArray()) {
            // Try alternative path
            transcriptData = root.path("transcriptSearchPanelRenderer")
                    .path("body")
                    .path("transcriptSegmentListRenderer")
                    .path("initialSegments");
        }
        
        if (transcriptData.isMissingNode() || !transcriptData.isArray()) {
            logger.error("No transcript data found in InnerTube response");
            throw new Exception("No transcript data found");
        }
        
        for (JsonNode cueGroup : transcriptData) {
            JsonNode cue = cueGroup.path("transcriptCueGroupRenderer")
                    .path("formattedStartOffset")
                    .path("simpleText");
            
            JsonNode cueText = cueGroup.path("transcriptCueGroupRenderer")
                    .path("cues")
                    .get(0)
                    .path("transcriptCueRenderer")
                    .path("cue")
                    .path("simpleText");
            
            if (!cue.isMissingNode() && !cueText.isMissingNode()) {
                TranscriptEntry entry = new TranscriptEntry();
                entry.setText(cueText.asText());
                
                // Parse time from format like "0:15" or "1:23:45"
                String timeStr = cue.asText();
                entry.setStart(parseTimeToSeconds(timeStr));
                entry.setDuration(5.0); // Default duration
                
                entries.add(entry);
            }
        }
        
        logger.info("Parsed {} transcript entries from InnerTube response", entries.size());
        return entries;
    }
    
    private double parseTimeToSeconds(String timeStr) {
        String[] parts = timeStr.split(":");
        double seconds = 0;
        
        try {
            if (parts.length == 2) {
                // MM:SS
                seconds = Integer.parseInt(parts[0]) * 60 + Integer.parseInt(parts[1]);
            } else if (parts.length == 3) {
                // HH:MM:SS
                seconds = Integer.parseInt(parts[0]) * 3600 + Integer.parseInt(parts[1]) * 60 + Integer.parseInt(parts[2]);
            }
        } catch (NumberFormatException e) {
            logger.warn("Failed to parse time: {}", timeStr);
        }
        
        return seconds;
    }
}