package com.youtube.transcript.controller;

import com.youtube.transcript.model.TranscriptEntry;
import com.youtube.transcript.model.TranscriptResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/test")
public class TestTranscriptController {
    
    @GetMapping("/transcript")
    public ResponseEntity<TranscriptResponse> getTestTranscript() {
        TranscriptResponse response = new TranscriptResponse();
        response.setVideoId("test123");
        response.setTitle("Test Video");
        response.setSuccess(true);
        
        List<TranscriptEntry> entries = new ArrayList<>();
        
        TranscriptEntry entry1 = new TranscriptEntry();
        entry1.setText("This is a test transcript");
        entry1.setStart(0.0);
        entry1.setDuration(5.0);
        entries.add(entry1);
        
        TranscriptEntry entry2 = new TranscriptEntry();
        entry2.setText("The system is working correctly");
        entry2.setStart(5.0);
        entry2.setDuration(3.0);
        entries.add(entry2);
        
        response.setTranscript(entries);
        
        return ResponseEntity.ok(response);
    }
}