{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mvn clean:*)", "Bash(./mvnw clean compile)", "Bash(pip install:*)", "Bash(python3 -m pip install:*)", "Bash(rm:*)", "Bash(./mvnw compile:*)", "Bash(./mvnw:*)", "Bash(ls:*)", "Bash(export:*)", "Bash(export PATH=\"$JAVA_HOME/bin:$PATH\")", "Bash(\"/mnt/c/Program Files/Java/jdk-21/bin/java\" -version)", "Bash(\"/mnt/c/Program Files/Java/jdk-21/bin/java.exe\" -version)", "Bash(JAVA_HOME=\"/mnt/c/Program Files/Java/jdk-21\" PATH=\"/mnt/c/Program Files/Java/jdk-21/bin:$PATH\" ./mvnw clean compile spring-boot:run)", "Bash(powershell.exe:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run.sh)", "Bash(/mnt/c/Users/<USER>/Documents/dev/july/youtube-transcribe/mvnw.cmd clean compile spring-boot:run)", "Bash(cmd.exe /c \"mvnw.cmd clean compile spring-boot:run\")", "Bash(cmd.exe:*)", "<PERSON><PERSON>(python:*)", "Bash(./yt-dlp --version)", "Bash(./test-simple.sh:*)", "Bash(grep:*)"], "deny": []}}